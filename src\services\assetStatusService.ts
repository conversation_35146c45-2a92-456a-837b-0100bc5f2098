// Frontend Asset Status Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface AssetStatus {
  id: string;
  asset_status: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AssetStatusCreationData {
  asset_status: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class AssetStatusService {
  // Get all asset statuses
  async getAllAssetStatuses(): Promise<{ assetStatuses: AssetStatus[]; count: number }> {
    try {
      const response = await apiCall('/asset-statuses');
      if (response.success) {
        return {
          assetStatuses: response.data.assetStatuses,
          count: response.data.count
        };
      }
      throw new Error(response.message || 'Failed to fetch asset statuses');
    } catch (error) {
      console.error('Error fetching asset statuses:', error);
      throw error;
    }
  }

  // Get asset status by ID
  async getAssetStatusById(id: string): Promise<AssetStatus> {
    try {
      const response = await apiCall(`/asset-statuses/${id}`);
      if (response.success) {
        return response.data.assetStatus;
      }
      throw new Error(response.message || 'Failed to fetch asset status');
    } catch (error) {
      console.error('Error fetching asset status by ID:', error);
      throw error;
    }
  }

  // Create new asset status
  async createAssetStatus(assetStatusData: AssetStatusCreationData): Promise<AssetStatus> {
    try {
      const response = await apiCall('/asset-statuses', {
        method: 'POST',
        body: JSON.stringify(assetStatusData),
      });
      if (response.success) {
        return response.data.assetStatus;
      }
      throw new Error(response.message || 'Failed to create asset status');
    } catch (error) {
      console.error('Error creating asset status:', error);
      throw error;
    }
  }

  // Update asset status
  async updateAssetStatus(id: string, assetStatusData: Partial<AssetStatusCreationData>): Promise<AssetStatus> {
    try {
      const response = await apiCall(`/asset-statuses/${id}`, {
        method: 'PUT',
        body: JSON.stringify(assetStatusData),
      });
      if (response.success) {
        return response.data.assetStatus;
      }
      throw new Error(response.message || 'Failed to update asset status');
    } catch (error) {
      console.error('Error updating asset status:', error);
      throw error;
    }
  }

  // Delete asset status
  async deleteAssetStatus(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/asset-statuses/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting asset status:', error);
      throw error;
    }
  }

  // Helper method to prepare asset statuses for export
  prepareAssetStatusesForExport(assetStatuses: AssetStatus[]) {
    return assetStatuses.map(assetStatus => ({
      ID: assetStatus.id,
      'Asset Status': assetStatus.asset_status,
      'Status': assetStatus.status ? 'Active' : 'Inactive',
      'Created At': new Date(assetStatus.createdAt).toLocaleDateString(),
      'Updated At': new Date(assetStatus.updatedAt).toLocaleDateString()
    }));
  }
}

const assetStatusService = new AssetStatusService();
export default assetStatusService;