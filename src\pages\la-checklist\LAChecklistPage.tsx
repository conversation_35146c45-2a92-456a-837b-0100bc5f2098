import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tablet, Scanner, Printer, CheckCircle, Clock, AlertTriangle } from "lucide-react";
import { workflowService } from "@/services/workflowService";
import { assetService } from "@/services/assetService";

// API service for L&A checklist
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

const laChecklistService = {
  async getAvailableAssets() {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${API_BASE_URL}/la-checklist/available-assets`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    return response.json();
  },

  async getSessions() {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${API_BASE_URL}/la-checklist/sessions`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    return response.json();
  }
};

const LAChecklistPage = () => {
  // Backend data states
  const [laStats, setLaStats] = useState({
    totalAssets: 0,
    testedAssets: 0,
    pendingAssets: 0,
    failedAssets: 0,
    testedPercentage: 0,
    pendingPercentage: 0,
    failedPercentage: 0
  });
  const [availableAssets, setAvailableAssets] = useState<any[]>([]);
  const [laSessions, setLaSessions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load L&A checklist data
  useEffect(() => {
    const loadLAData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch available assets for L&A testing (New and Ready status)
        const availableAssets = await laChecklistService.getAvailableAssets();

        // Fetch L&A sessions
        const sessions = await laChecklistService.getSessions();

        // Set the data directly (your backend returns the data directly)
        setAvailableAssets(availableAssets || []);
        setLaSessions(sessions || []);

        // Calculate statistics from real data
        const totalAssets = availableAssets.length;
        const testedAssets = sessions.filter((s: any) => s.status === 'Completed').length;
        const pendingAssets = sessions.filter((s: any) => s.status === 'In Progress' || s.status === 'Pending').length;
        const failedAssets = sessions.filter((s: any) => s.status === 'Failed').length;

        setLaStats({
          totalAssets,
          testedAssets,
          pendingAssets,
          failedAssets,
          testedPercentage: totalAssets > 0 ? Math.round((testedAssets / totalAssets) * 100) : 0,
          pendingPercentage: totalAssets > 0 ? Math.round((pendingAssets / totalAssets) * 100) : 0,
          failedPercentage: totalAssets > 0 ? Math.round((failedAssets / totalAssets) * 100) : 0
        });

      } catch (err) {
        console.error('Error loading L&A data:', err);
        setError('Failed to load L&A checklist data');
      } finally {
        setLoading(false);
      }
    };

    loadLAData();
  }, []);

  return (
    <AppLayout>
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">Logic & Accuracy Checklist</h1>
            <p className="text-muted-foreground">
              Manage and track Logic & Accuracy testing for election equipment
            </p>
          </div>
          <div className="flex gap-2">
            <Button>
              New L&A Test Session
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card className="col-span-3">
            <CardHeader className="pb-2">
              <CardTitle>L&A Testing Progress</CardTitle>
              <CardDescription>
                Overall progress of Logic & Accuracy testing for the upcoming election
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-muted-foreground">Loading L&A statistics...</span>
                </div>
              ) : error ? (
                <div className="text-center py-8 text-red-600">
                  <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                  <p>{error}</p>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                    <div className="flex items-center">
                      <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mr-4">
                        <CheckCircle className="h-8 w-8 text-green-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{laStats.testedPercentage}%</p>
                        <p className="text-sm text-muted-foreground">Equipment Tested ({laStats.testedAssets})</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mr-4">
                        <Clock className="h-8 w-8 text-yellow-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{laStats.pendingPercentage}%</p>
                        <p className="text-sm text-muted-foreground">In Progress ({laStats.pendingAssets})</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mr-4">
                        <AlertTriangle className="h-8 w-8 text-red-600" />
                      </div>
                      <div>
                        <p className="text-2xl font-bold">{laStats.failedPercentage}%</p>
                        <p className="text-sm text-muted-foreground">Failed ({laStats.failedAssets})</p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-green-600 h-2.5 rounded-l-full"
                        style={{ width: `${laStats.testedPercentage}%` }}
                      ></div>
                      <div
                        className="bg-yellow-500 h-2.5"
                        style={{
                          width: `${laStats.pendingPercentage}%`,
                          marginLeft: `${laStats.testedPercentage}%`
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground mt-2">
                      <span>Total Assets: {laStats.totalAssets}</span>
                      <span>Remaining: {laStats.totalAssets - laStats.testedAssets - laStats.pendingAssets - laStats.failedAssets}</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        <h2 className="text-xl font-semibold mb-4">Equipment Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Link to="/la-checklist/pollpads">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Pollpads</CardTitle>
                  <Tablet className="h-6 w-6 text-blue-500" />
                </div>
                <CardDescription>
                  Logic & Accuracy testing for Pollpads
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: "85%" }}></div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Units: 120</span>
                    <span className="text-green-600">102 Completed</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link to="/la-checklist/scanners">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Scanners</CardTitle>
                  <Scanner className="h-6 w-6 text-purple-500" />
                </div>
                <CardDescription>
                  Logic & Accuracy testing for Scanners
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span className="font-medium">60%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: "60%" }}></div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Units: 80</span>
                    <span className="text-green-600">48 Completed</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link to="/la-checklist/bmd">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>BMDs</CardTitle>
                  <Printer className="h-6 w-6 text-green-500" />
                </div>
                <CardDescription>
                  Logic & Accuracy testing for Ballot Marking Devices
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span className="font-medium">45%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: "45%" }}></div>
                    </div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Units: 200</span>
                    <span className="text-green-600">90 Completed</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        <h2 className="text-xl font-semibold mb-4">Recent L&A Test Sessions</h2>
        <div className="border rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium">Session ID</th>
                <th className="text-left py-3 px-4 font-medium">Equipment Type</th>
                <th className="text-left py-3 px-4 font-medium">County</th>
                <th className="text-left py-3 px-4 font-medium">Date</th>
                <th className="text-left py-3 px-4 font-medium">Technician</th>
                <th className="text-left py-3 px-4 font-medium">Units Tested</th>
                <th className="text-left py-3 px-4 font-medium">Status</th>
                <th className="text-left py-3 px-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1001</td>
                <td className="py-3 px-4">Pollpads</td>
                <td className="py-3 px-4">Richland</td>
                <td className="py-3 px-4">04/15/2023</td>
                <td className="py-3 px-4">John Smith</td>
                <td className="py-3 px-4">25</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    Completed
                  </span>
                </td>
                <td className="py-3 px-4">
                  <Button variant="ghost" size="sm">View</Button>
                </td>
              </tr>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1002</td>
                <td className="py-3 px-4">Scanners</td>
                <td className="py-3 px-4">Lexington</td>
                <td className="py-3 px-4">04/16/2023</td>
                <td className="py-3 px-4">Sarah Johnson</td>
                <td className="py-3 px-4">15</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    Completed
                  </span>
                </td>
                <td className="py-3 px-4">
                  <Button variant="ghost" size="sm">View</Button>
                </td>
              </tr>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1003</td>
                <td className="py-3 px-4">BMDs</td>
                <td className="py-3 px-4">Charleston</td>
                <td className="py-3 px-4">04/17/2023</td>
                <td className="py-3 px-4">Michael Brown</td>
                <td className="py-3 px-4">30</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                    In Progress
                  </span>
                </td>
                <td className="py-3 px-4">
                  <Button variant="ghost" size="sm">View</Button>
                </td>
              </tr>
              <tr className="border-t">
                <td className="py-3 px-4">LA-1004</td>
                <td className="py-3 px-4">Pollpads</td>
                <td className="py-3 px-4">Greenville</td>
                <td className="py-3 px-4">04/18/2023</td>
                <td className="py-3 px-4">Emily Davis</td>
                <td className="py-3 px-4">20</td>
                <td className="py-3 px-4">
                  <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                    In Progress
                  </span>
                </td>
                <td className="py-3 px-4">
                  <Button variant="ghost" size="sm">View</Button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </AppLayout>
  );
};

export default LAChecklistPage;
