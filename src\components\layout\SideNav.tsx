import React, { useState, useEffect, useRef } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  ChevronDown,
  ChevronRight,
  Database,
  FileCheck,
  FileText,
  List,
  Package,
  PackageCheck,
  Settings,
  User,
  Users,
  Wrench
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useSidebar } from "@/contexts/SidebarContext";

interface SubNavItem {
  label: string;
  path: string;
}

interface NavItem {
  label: string;
  icon: React.ElementType;
  path: string;
  subItems?: SubNavItem[];
  subPages?: { [key: string]: SubNavItem[] };
}

const navItems: NavItem[] = [
  {
    label: "Dashboard",
    icon: Database,
    path: "/dashboard"
  },
  {
    label: "Masters",
    icon: List,
    path: "/masters",
    subItems: [
      { label: "Elections", path: "/masters/elections" },
      { label: "Location", path: "/masters/location" },
      { label: "Vendors", path: "/masters/vendors" },
      { label: "Assets", path: "/masters/assets" },
      { label: "Consumables", path: "/masters/consumables" },
      { label: "Packing List", path: "/masters/packing-list" },
      { label: "Rolling Cage", path: "/masters/rolling-cage" },
      { label : "Warranty Management", path: "/masters/warranty" },

    ]
  },
  {
    label: "Transactions",
    icon: PackageCheck,
    path: "/transactions",
    subItems: [
      { label: "Assets", path: "/transactions/assets" },
      { label: "Consumables", path: "/transactions/consumables" },
      { label: "Damage Report", path: "/transactions/damage-reports" },
      { label: "Transfer", path: "/transactions/transfer" }
    ],
    subPages: {
      "/transactions/assets": [
        { label: "Check In", path: "/transactions/assets/checkin" },
        { label: "Check Out", path: "/transactions/assets/checkout" },
      ],
      "/transactions/consumables": [
        { label: "Orders", path: "/transactions/consumables/orders" },
        { label: "Stock", path: "/transactions/consumables/stock" },
        { label: "Check Out", path: "/transactions/consumables/checkout" }
      ],
      // "/transactions/damage-reports": [
      //   { label: "Report Damage", path: "/transactions/damage-reports/report-damage" },
      //   { label: "View Reports", path: "/transactions/damage-reports/view-reports" },
      //   { label: "Track Status", path: "/transactions/damage-reports/track-status" }
      // ],
      // "/transactions/transfer": [
      //   { label: "Create Transfer", path: "/transactions/transfer/create-transfer" },
      //   { label: "Track Transfer", path: "/transactions/transfer/track-transfer" },
      //   { label: "Transfer History", path: "/transactions/transfer/transfer-history" }
      // ]
    }
  },
  {
    label: "L&A Checklist",
    icon: FileCheck,
    path: "/la-checklist",
    subItems: [
      { label: "Pollpads", path: "/la-checklist/pollpads" },
      { label: "Scanners", path: "/la-checklist/scanners" },
      { label: "BMD", path: "/la-checklist/bmd" }
    ],
    subPages: {
      "/la-checklist/pollpads": [
        { label : "Voter Card Testing", path: "/la-checklist/pollpads/voter-card-testing" },
        { label : "Voter File Loaded", path: "/la-checklist/pollpads/voter-file-loaded" },
      ],
  },
},
  
  {
    label: "Supply Checklist",
    icon: Package,
    path: "/supply-checklist",
    subItems: [
      { label: "Election Readiness", path: "/supply-checklist/election-readiness" },
      { label: "Packing List", path: "/supply-checklist/packing-list" },
      { label: "Rolling Cage", path: "/supply-checklist/rolling-cage" },
      
    ],
    subPages: {
      "/supply-checklist/packing-list": [
        { label: "Pack", path: "/supply-checklist/packing-list/pack" },
        { label: "Proof", path: "/supply-checklist/packing-list/proof" },
        { label: "Complete", path: "/supply-checklist/packing-list/complete" },
        { label: "Unpack", path: "/supply-checklist/packing-list/unpack" }
      ],
      "/supply-checklist/rolling-cage": [
        { label: "Pack", path: "/supply-checklist/rolling-cage/pack" },
        { label: "Proof", path: "/supply-checklist/rolling-cage/proof" },
        { label: "Complete", path: "/supply-checklist/rolling-cage/complete" },
        { label: "Unpack", path: "/supply-checklist/rolling-cage/unpack" },
        { label: "Detail", path: "/supply-checklist/rolling-cage/detail" }
      ]
    }
  },
  {
    label: "Maintenance",
    icon: Wrench,
    path: "/maintenance"
  },
  {
    label: "Reports & Analytics",
    icon: FileText,
    path: "/reports-analytics",
    subItems: [
      { label: "Custom Reports", path: "/reports-analytics/custom" },
      { label: "Scheduled Reports", path: "/reports-analytics/scheduled" },
      { label: "Audit Reports", path: "/reports-analytics/audit" }
    ],
    subPages: {
      "/reports-analytics/audit": [
        { label: "User Activity Logs", path: "/reports-analytics/audit/user-activity" },
        { label: "Asset Movement History", path: "/reports-analytics/audit/asset-movement" },
        { label: "System Access Reports", path: "/reports-analytics/audit/system-access" },
        { label: "Change Management Logs", path: "/reports-analytics/audit/change-management" },
        { label: "Asset Scan Logs", path: "/reports-analytics/audit/asset-scan" }
      ]
    }
  },
  
  {
    label: "Configuration",
    icon: Settings,
    path: "/configuration",
    subItems: [
      { label: "Settings", path: "/configuration/settings" },
      { label: "API Configuration", path: "/configuration/api" },
    ],
    subPages: {
      "/configuration/settings": [
        { label: "Asset Types", path: "/configuration/settings/asset-type" },
        { label: "Election Types", path: "/configuration/settings/election-type" },

        { label: "Asset Models", path: "/configuration/settings/asset-model" },
        
        //{ label: "Consumable Models", path: "/configuration/settings/consumable-models" },
        { label: "Consumable Categories", path: "/configuration/settings/consumables-category" },
        { label: "Other Cost Types", path: "/configuration/settings/other-cost-type" },
        { label: "Packing Locations", path: "/configuration/settings/location" },
        // { label: "Packing List Master", path: "/configuration/settings/packing-list-master" },
        // { label: "Rolling Cage Master", path: "/configuration/settings/rolling-cage-master" },
        { label: "Service Maintenance Types", path: "/configuration/settings/service-maintenance-type" },
      ] },
    
  },
  {
    label: "User Management",
    icon: Users,
    path: "/user-management",
    subItems: [
      { label: "Users", path: "/user-management/users" },
      { label: "User Groups", path: "/user-management/user-groups" },
      { label: "Privileges", path: "/user-management/privileges" }
    ]
  },
 
];

export function SideNav() {
  const { collapsed, toggleCollapsed } = useSidebar();
  const location = useLocation();
  const isFirstRender = useRef(true);
  
  // Function to get expanded states based on current path
  const getExpandedStatesForPath = (currentPath: string) => {
    const expandedItems: string[] = [];
    const expandedSubPages: string[] = [];

    navItems.forEach(item => {
      // Check if current path starts with this item's path
      if (currentPath.startsWith(item.path + '/') || currentPath === item.path) {
        if (item.subItems && item.subItems.length > 0) {
          expandedItems.push(item.path);
        }

        // Check sub items
        item.subItems?.forEach(subItem => {
          if (currentPath.startsWith(subItem.path + '/') || currentPath === subItem.path) {
            // Check if this sub item has sub pages
            if (item.subPages && item.subPages[subItem.path]) {
              expandedSubPages.push(subItem.path);
            }
          }
        });
      }
    });

    return { expandedItems, expandedSubPages };
  };

  // Initialize states with current location
  const initialStates = getExpandedStatesForPath(location.pathname);
  const [expandedItems, setExpandedItems] = useState<string[]>(initialStates.expandedItems);
  const [expandedSubPages, setExpandedSubPages] = useState<string[]>(initialStates.expandedSubPages);

  // Update expanded states when location changes, but avoid initial animation
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const currentPath = location.pathname;
    const newStates = getExpandedStatesForPath(currentPath);
    
    // Add new items to expanded states without causing collapse/expand animation
    setExpandedItems(prev => {
      const shouldExpand = newStates.expandedItems.filter(item => !prev.includes(item));
      if (shouldExpand.length > 0) {
        return [...prev, ...shouldExpand];
      }
      return prev;
    });
    
    setExpandedSubPages(prev => {
      const shouldExpand = newStates.expandedSubPages.filter(item => !prev.includes(item));
      if (shouldExpand.length > 0) {
        return [...prev, ...shouldExpand];
      }
      return prev;
    });
  }, [location.pathname]);

  const toggleExpanded = (path: string) => {
    setExpandedItems(prev => 
      prev.includes(path) 
        ? prev.filter(item => item !== path)
        : [...prev, path]
    );
  };

  const toggleSubPageExpanded = (path: string) => {
    setExpandedSubPages(prev => 
      prev.includes(path) 
        ? prev.filter(item => item !== path)
        : [...prev, path]
    );
  };

  const renderNavItem = (item: NavItem) => {
    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = expandedItems.includes(item.path);
    const isActive = location.pathname === item.path || location.pathname.startsWith(item.path + '/');

    return (
      <div key={item.path} className="mb-1">
        <div
          className={cn(
            "flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-200 cursor-pointer",
            isActive
              ? "bg-white/20 text-white shadow-sm"
              : "text-white/80 hover:bg-white/10 hover:text-white"
          )}
          onClick={() => hasSubItems ? toggleExpanded(item.path) : null}
        >
          <Link
            to={item.path}
            className="flex items-center space-x-2 flex-1"
            onClick={(e) => hasSubItems && e.preventDefault()}
          >
            <item.icon className="h-5 w-5 transition-transform duration-200" />
            {!collapsed && <span className="transition-opacity duration-200">{item.label}</span>}
          </Link>
          {!collapsed && hasSubItems && (
            <div 
              onClick={(e) => e.stopPropagation()}
              className="transition-transform duration-200 hover:scale-110"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4 transition-transform duration-200" />
              ) : (
                <ChevronRight className="h-4 w-4 transition-transform duration-200" />
              )}
            </div>
          )}
        </div>

        {/* Sub Items */}
        {!collapsed && hasSubItems && (
          <div 
            className={cn(
              "ml-4 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out",
              isExpanded 
                ? "max-h-96 opacity-100" 
                : "max-h-0 opacity-0"
            )}
          >
            {item.subItems?.map((subItem) => {
              const hasSubPages = item.subPages && item.subPages[subItem.path];
              const isSubExpanded = expandedSubPages.includes(subItem.path);
              const isSubActive = location.pathname === subItem.path || location.pathname.startsWith(subItem.path + '/');

              return (
                <div key={subItem.path} className="transition-all duration-200">
                  <div
                    className={cn(
                      "flex items-center space-x-2 px-3 py-1.5 rounded-md transition-all duration-200 cursor-pointer text-sm",
                      isSubActive
                        ? "bg-white/15 text-white shadow-sm"
                        : "text-white/70 hover:bg-white/10 hover:text-white"
                    )}
                    onClick={() => hasSubPages ? toggleSubPageExpanded(subItem.path) : null}
                  >
                    <Link
                      to={subItem.path}
                      className="flex items-center space-x-2 flex-1"
                      onClick={(e) => hasSubPages && e.preventDefault()}
                    >
                      <span className="transition-opacity duration-200">{subItem.label}</span>
                    </Link>
                    {hasSubPages && (
                      <div 
                        onClick={(e) => e.stopPropagation()}
                        className="transition-transform duration-200 hover:scale-110"
                      >
                        {isSubExpanded ? (
                          <ChevronDown className="h-3 w-3 transition-transform duration-200" />
                        ) : (
                          <ChevronRight className="h-3 w-3 transition-transform duration-200" />
                        )}
                      </div>
                    )}
                  </div>

                  {/* Sub Pages */}
                  {hasSubPages && (
                    <div 
                      className={cn(
                        "ml-4 mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-in-out",
                        isSubExpanded 
                          ? "max-h-64 opacity-100" 
                          : "max-h-0 opacity-0"
                      )}
                    >
                      {item.subPages![subItem.path].map((subPage) => (
                        <Link
                          key={subPage.path}
                          to={subPage.path}
                          className={cn(
                            "block px-3 py-1 rounded-md transition-all duration-200 text-xs",
                            location.pathname === subPage.path
                              ? "bg-white/10 text-white shadow-sm"
                              : "text-white/60 hover:bg-white/5 hover:text-white hover:translate-x-1"
                          )}
                        >
                          {subPage.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        "border-r bg-[#1e3a8a] text-white transition-all duration-300 ease-in-out flex flex-col",
        collapsed ? "w-16" : "w-64",
        "fixed left-0 top-0 h-full z-30 shadow-lg"
      )}
    >
      {/* Logo Section */}
      <div className="p-4 border-b border-white/10 flex flex-col items-center">
        <div className="flex justify-center items-center w-full mb-4 transition-all duration-300">
          {collapsed ? (
            <img 
              src="/sidebar.png" 
              alt="Logo" 
              className="h-10 w-10 transition-all duration-300" 
            />
          ) : (
            <img 
              src="/logo.jpg" 
              alt="Logo" 
              className="h-14 w-auto transition-all duration-300" 
            />
          )}
        </div>
        <Button
          onClick={toggleCollapsed}
          size="sm"
          variant="ghost"
          className="text-white hover:bg-white/10 h-8 w-8 p-0 transition-all duration-200 hover:scale-110"
        >
          <span className={cn(
            "transition-transform duration-300",
            collapsed ? "rotate-0" : "rotate-180"
          )}>
            {collapsed ? ">" : "<"}
          </span>
        </Button>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
        <nav className="px-2 py-4">
          {navItems.map(renderNavItem)}
        </nav>
      </div>

      {/* Profile Link */}
      <div className="p-4 border-t border-white/10">
        <Link
          to="/profile"
          className={cn(
            "flex items-center space-x-2 px-3 py-2 rounded-md transition-all duration-200",
            location.pathname === "/profile"
              ? "bg-white/20 text-white shadow-sm"
              : "text-white/80 hover:bg-white/10 hover:text-white"
          )}
        >
          <User className="h-5 w-5 transition-transform duration-200" />
          {!collapsed && (
            <span className="transition-opacity duration-200">Profile</span>
          )}
        </Link>
      </div>
    </div>
  );
}
