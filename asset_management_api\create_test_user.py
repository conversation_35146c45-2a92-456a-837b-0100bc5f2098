#!/usr/bin/env python3
"""
Create a test user for API testing.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from app.models.user import User
from sqlalchemy.orm import Session
import uuid
import bcrypt
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_user():
    """Create a test user for API testing."""
    
    db = next(get_db())
    
    try:
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("✅ Test user already exists: <EMAIL>")
            return True
        
        # Create test user
        hashed_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        test_user = User(
            id=str(uuid.uuid4()),
            username="admin",
            email="<EMAIL>",
            login_id="admin",
            password_hash=hashed_password,
            first_name="Test",
            last_name="Admin",
            role="admin",
            is_active=True,
            is_verified=True
        )
        
        db.add(test_user)
        db.commit()
        
        print("✅ Test user created successfully!")
        print("   Email: <EMAIL>")
        print("   Password: admin123")
        print("   Role: admin")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating test user: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Creating test user for API testing...")
    success = create_test_user()
    if success:
        print("✅ Test user ready for API testing!")
    else:
        print("❌ Failed to create test user.")
        sys.exit(1)
