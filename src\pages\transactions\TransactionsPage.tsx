import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  Package,
  PackageCheck,
  FileDown,
  Plus,
  MoreVertical,
  ArrowRight,
  Clock,
  ArrowDown,
  ChevronDown,
  Truck,
  FileText,
  Settings
} from "lucide-react";
import { PageTitle } from "@/components/layout/PageTitle";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { workflowService } from "@/services/workflowService";
import { transactionOrderService } from "@/services/transactionorderService";
import { AlertTriangle } from "lucide-react";

export default function TransactionsPage() {
  // Backend data states
  const [recentAssetTransactions, setRecentAssetTransactions] = useState<any[]>([]);
  const [recentConsumableTransactions, setRecentConsumableTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load recent transactions
  useEffect(() => {
    const loadRecentTransactions = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch recent asset transactions (using workflow stats as proxy)
        const workflowStatsResponse = await workflowService.getWorkflowStats();
        
        // Fetch recent consumable transactions
        const recentOrdersResponse = await transactionOrderService.getRecentOrders();

        // Mock recent asset transactions from workflow stats
        if (workflowStatsResponse.success) {
          const mockAssetTransactions = [
            { id: 1, asset: "BMD-001", type: "Check Out", user: "John Doe", created_by: "johndoe" },
            { id: 2, asset: "BMD-002", type: "Check In", user: "Jane Smith", created_by: "janesmith" },
            { id: 3, asset: "BMD-003", type: "Check Out", user: "Bob Wilson", created_by: "bobwilson" }
          ];
          setRecentAssetTransactions(mockAssetTransactions);
        }

        // Use recent orders for consumable transactions
        if (recentOrdersResponse && recentOrdersResponse.length > 0) {
          const consumableTransactions = recentOrdersResponse.slice(0, 5).map((order: any) => ({
            id: order.id,
            item: order.consumable,
            consumable_name: order.consumable,
            type: "Order",
            quantity: order.totalQty || order.quantity || 1
          }));
          setRecentConsumableTransactions(consumableTransactions);
        }

      } catch (err) {
        console.error('Error loading recent transactions:', err);
        setError('Failed to load recent transactions');
        // Set empty arrays as fallback
        setRecentAssetTransactions([]);
        setRecentConsumableTransactions([]);
      } finally {
        setLoading(false);
      }
    };

    loadRecentTransactions();
  }, []);
  return (
    <AppLayout>
      <div className="space-y-6 p-6">
        <PageTitle
          title="Transactions"
          description="Manage asset and consumable transactions"
          actions={
            <>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="min-w-[140px]">
                          <FileDown className="h-4 w-4 mr-2" />
                          Export <ChevronDown className="h-4 w-4 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[200px]">
                        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <FileText className="h-4 w-4 mr-2" />
                          Export All Transactions
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <PackageCheck className="h-4 w-4 mr-2" />
                          Export Asset Transactions
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Package className="h-4 w-4 mr-2" />
                          Export Consumable Transactions
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Settings className="h-4 w-4 mr-2" />
                          Export Settings
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <p>Export transaction records</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          }
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Asset Transactions Card */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-center mb-2">
                <div>
                  <CardTitle className="text-xl font-semibold flex items-center">
                    <PackageCheck className="h-5 w-5 text-blue-500 mr-2" />
                    Asset Transactions
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Manage equipment check-ins and check-outs
                  </CardDescription>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/assets">
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>View all asset transactions</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="flex gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/assets/checkout" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          Check Out Assets
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>Create new asset check-out</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/assets/checkin" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          Check In Assets
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>Process asset returns</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Recent Activity</span>
                    <span className="font-medium text-blue-600">24 transactions this week</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: "65%" }}></div>
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  {loading ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
                      <span className="text-sm text-muted-foreground">Loading transactions...</span>
                    </div>
                  ) : error ? (
                    <div className="text-center py-4 text-red-600">
                      <AlertTriangle className="h-5 w-5 mx-auto mb-1" />
                      <p className="text-sm">{error}</p>
                    </div>
                  ) : recentAssetTransactions.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      <p className="text-sm">No recent asset transactions</p>
                    </div>
                  ) : (
                    recentAssetTransactions.map((transaction) => (
                      <div key={transaction.id}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg text-sm hover:bg-gray-100 transition-colors">
                        <div className="flex items-center gap-3">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <div>
                            <span className="font-medium text-gray-900">{transaction.asset || transaction.asset_id}</span>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                              transaction.type === "Check Out"
                                ? "bg-blue-100 text-blue-700"
                                : "bg-green-100 text-green-700"
                            }`}>
                              {transaction.type}
                            </span>
                          </div>
                        </div>
                        <div className="text-gray-500 text-sm">
                          {transaction.user || transaction.created_by}
                        </div>
                      </div>
                    ))
                  )}
                </div>

                <div className="flex justify-between items-center pt-4">
                  <Link to="/transactions/assets/tracking">
                    <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                      <Truck className="h-4 w-4 mr-2" />
                      Track Assets
                    </Button>
                  </Link>
                  <Link to="/transactions/assets">
                    <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                      View All
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Consumable Transactions Card */}
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-center mb-2">
                <div>
                  <CardTitle className="text-xl font-semibold flex items-center">
                    <Package className="h-5 w-5 text-purple-500 mr-2" />
                    Consumable Transactions
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Manage orders and inventory
                  </CardDescription>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/consumables">
                        <Button variant="ghost" size="sm">
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>View all consumable transactions</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="flex gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/consumables/orders" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          Manage Orders
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>Create and track orders</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link to="/transactions/consumables/stock" className="flex-1">
                        <Button variant="outline" size="sm" className="w-full">
                          Manage Stock
                        </Button>
                      </Link>
                    </TooltipTrigger>
                    <TooltipContent>Update inventory levels</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-gray-600">Recent Activity</span>
                    <span className="font-medium text-purple-600">12 transactions this week</span>
                  </div>
                  <div className="w-full bg-gray-100 rounded-full h-2">
                    <div className="bg-purple-500 h-2 rounded-full" style={{ width: "40%" }}></div>
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  {loading ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mr-2"></div>
                      <span className="text-sm text-muted-foreground">Loading transactions...</span>
                    </div>
                  ) : error ? (
                    <div className="text-center py-4 text-red-600">
                      <AlertTriangle className="h-5 w-5 mx-auto mb-1" />
                      <p className="text-sm">{error}</p>
                    </div>
                  ) : recentConsumableTransactions.length === 0 ? (
                    <div className="text-center py-4 text-gray-500">
                      <p className="text-sm">No recent consumable transactions</p>
                    </div>
                  ) : (
                    recentConsumableTransactions.map((transaction) => (
                      <div key={transaction.id}
                        className="flex items-center justify-between p-3 bg-gray-50 rounded-lg text-sm hover:bg-gray-100 transition-colors">
                        <div className="flex items-center gap-3">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <div>
                            <span className="font-medium text-gray-900">{transaction.item || transaction.consumable_name}</span>
                            <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${
                              transaction.type === "Order"
                                ? "bg-purple-100 text-purple-700"
                                : "bg-orange-100 text-orange-700"
                            }`}>
                              {transaction.type}
                            </span>
                          </div>
                        </div>
                      <div className={`text-sm font-medium ${
                        transaction.quantity > 0 
                          ? "text-green-600" 
                          : "text-red-600"
                      }`}>
                        {transaction.quantity > 0 ? "+" : ""}{transaction.quantity}
                      </div>
                    </div>
                  ))
                  )}
                </div>

                <div className="flex justify-between items-center pt-4">
                  <Link to="/transactions/consumables/checkout-list">
                    <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                      <Package className="h-4 w-4 mr-2" />
                      Checkouts
                    </Button>
                  </Link>
                  <Link to="/transactions/consumables">
                    <Button variant="ghost" size="sm" className="text-purple-600 hover:text-purple-700">
                      View All
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
