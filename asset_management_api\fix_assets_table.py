#!/usr/bin/env python3
"""
Fix Assets Table Schema
This script adds missing columns to the assets table to match the model.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_assets_table():
    """Add missing columns to assets table."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.begin() as conn:  # Use begin() for auto-commit
            # Check current table structure
            logger.info("🔍 Checking current assets table structure...")

            result = conn.execute(text("DESCRIBE assets"))
            existing_columns = [row[0] for row in result.fetchall()]
            logger.info(f"Existing columns: {existing_columns}")

            # Define required columns that might be missing
            required_columns = {
                'purchase_date': 'DATETIME NULL',
                'warranty_expiry': 'DATETIME NULL',
                'last_maintenance': 'DATETIME NULL',
                'next_maintenance': 'DATETIME NULL',
                'last_checked': 'DATETIME NULL',
                'notes': 'TEXT NULL',
                'specifications': 'TEXT NULL',
                'created_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP',
                'updated_at': 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            }

            # Add missing columns
            for column_name, column_def in required_columns.items():
                if column_name not in existing_columns:
                    logger.info(f"➕ Adding missing column: {column_name}")
                    alter_sql = f"ALTER TABLE assets ADD COLUMN {column_name} {column_def}"
                    conn.execute(text(alter_sql))
                    logger.info(f"✅ Added column: {column_name}")
                else:
                    logger.info(f"✅ Column already exists: {column_name}")

            # Verify final structure
            logger.info("🔍 Verifying final table structure...")
            result = conn.execute(text("DESCRIBE assets"))
            final_columns = [row[0] for row in result.fetchall()]
            logger.info(f"Final columns: {final_columns}")

            logger.info("🎉 Assets table schema fixed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error fixing assets table: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Fixing Assets Table Schema...")
    success = fix_assets_table()
    if success:
        print("✅ Assets table is now ready!")
        print("🚀 You can now restart your backend server.")
    else:
        print("❌ Failed to fix assets table.")
        sys.exit(1)
