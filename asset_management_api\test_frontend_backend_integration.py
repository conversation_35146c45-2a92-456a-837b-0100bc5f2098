#!/usr/bin/env python3
"""
Test script to verify complete workflow integration between frontend and backend.
This script tests all the API endpoints that the frontend is calling.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime

# API Configuration
API_BASE_URL = "http://localhost:8000/api"
TEST_USER = {
    "email": "<EMAIL>",
    "password": "admin123"
}

def get_auth_token():
    """Get authentication token."""
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login",
            json=TEST_USER,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_endpoint(endpoint, headers, method="GET", data=None):
    """Test a single endpoint."""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            response = requests.request(method, url, headers=headers, json=data)
        
        status_icon = "✅" if response.status_code < 400 else "❌"
        print(f"{status_icon} {method} {endpoint} - Status: {response.status_code}")
        
        if response.status_code < 400:
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    count = len(data['data']) if isinstance(data['data'], list) else 1
                    print(f"   📊 Data: {count} items")
                elif isinstance(data, list):
                    print(f"   📊 Data: {len(data)} items")
                else:
                    print(f"   📊 Response: {type(data).__name__}")
            except:
                print(f"   📊 Response: {len(response.text)} bytes")
        else:
            print(f"   ❌ Error: {response.text[:100]}...")
            
        return response.status_code < 400
        
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {e}")
        return False

def test_workflow_integration():
    """Test all workflow integration endpoints."""
    
    print("🚀 Testing Asset Management Workflow Integration")
    print("=" * 60)
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    success_count = 0
    total_tests = 0
    
    # Test categories
    test_categories = {
        "🏗️  Core Asset Management": [
            "/assets",
            "/assets/stats",
            "/asset-types",
            "/asset-models", 
            "/asset-status"
        ],
        
        "🔬 L&A Checklist Workflow": [
            "/la-checklist/available-assets",
            "/la-checklist/sessions"
        ],
        
        "📦 Supply Checklist Workflow": [
            "/supply-checklist/checklists",
        ],
        
        "📋 Packing & Rolling Cage": [
            "/packing-lists",
            "/rolling-cage"
        ],
        
        "💼 Transaction Management": [
            "/transaction-orders",
            "/transaction-orders/recent",
            "/asset-status-history/recent"
        ],
        
        "🔧 Workflow & Status": [
            "/workflow/stats",
            "/asset-status-history/summary"
        ],
        
        "🏪 Consumables Management": [
            "/masters/consumables",
            "/consumables-checkout"
        ],
        
        "👥 User & Configuration": [
            "/users/users",
            "/masters/locations",
            "/elections"
        ]
    }
    
    for category, endpoints in test_categories.items():
        print(f"\n{category}")
        print("-" * 40)
        
        for endpoint in endpoints:
            success = test_endpoint(endpoint, headers)
            if success:
                success_count += 1
            total_tests += 1
    
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    print(f"✅ Successful: {success_count}/{total_tests}")
    print(f"❌ Failed: {total_tests - success_count}/{total_tests}")
    print(f"📈 Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count >= total_tests * 0.8:  # 80% success rate
        print("\n🎉 Most workflow integrations are working!")
        print("✅ Your frontend should be properly connected to the backend")
    else:
        print(f"\n⚠️  {total_tests - success_count} endpoints need attention")
        print("🔧 Check the failed endpoints above")
    
    return success_count >= total_tests * 0.8

if __name__ == "__main__":
    print("🔧 Starting Frontend-Backend Integration Tests...")
    success = test_workflow_integration()
    
    if success:
        print("\n✅ Integration tests mostly passed! Your workflow integration is working.")
    else:
        print("\n❌ Many tests failed. Check the output above for details.")
        sys.exit(1)
