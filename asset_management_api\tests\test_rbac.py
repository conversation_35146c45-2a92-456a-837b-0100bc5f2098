#!/usr/bin/env python3
"""
Comprehensive tests for Role-Based Access Control (RBAC) functionality.
Tests data segregation, security measures, and hierarchical access control.
"""

import pytest
import sys
import os
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.main import app
from app.config.database import get_db, Base
from app.models.user import User
from app.models.assets import Asset
from app.models.asset_transfers import AssetTransfer, TransferStatus, TransferType
from app.middleware.rbac import RBACService
from uuid import uuid4

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///./test_rbac.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def setup_database():
    """Create test database and tables."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def db_session():
    """Create a fresh database session for each test."""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def client():
    """Create test client."""
    return TestClient(app)

@pytest.fixture
def test_users(db_session):
    """Create test users with different access levels."""
    users = {
        'admin': User(
            id=str(uuid4()),
            first_name="Admin",
            last_name="User",
            email="<EMAIL>",
            password="hashed_password",
            mobile="1234567890",
            user_group="Admin Group",
            login_id="admin",
            access_level="state",
            state="South Carolina",
            role="admin",
            status=True,
            login_enabled=True
        ),
        'state_user': User(
            id=str(uuid4()),
            first_name="State",
            last_name="User",
            email="<EMAIL>",
            password="hashed_password",
            mobile="1234567891",
            user_group="State Group",
            login_id="state_user",
            access_level="state",
            state="South Carolina",
            role="user",
            status=True,
            login_enabled=True
        ),
        'county_user': User(
            id=str(uuid4()),
            first_name="County",
            last_name="User",
            email="<EMAIL>",
            password="hashed_password",
            mobile="1234567892",
            user_group="County Group",
            login_id="county_user",
            access_level="county",
            state="South Carolina",
            county="Charleston",
            role="user",
            status=True,
            login_enabled=True
        ),
        'precinct_user': User(
            id=str(uuid4()),
            first_name="Precinct",
            last_name="User",
            email="<EMAIL>",
            password="hashed_password",
            mobile="1234567893",
            user_group="Precinct Group",
            login_id="precinct_user",
            access_level="precinct",
            state="South Carolina",
            county="Charleston",
            precinct="Precinct 1",
            role="user",
            status=True,
            login_enabled=True
        )
    }
    
    for user in users.values():
        db_session.add(user)
    db_session.commit()
    
    return users

@pytest.fixture
def test_assets(db_session, test_users):
    """Create test assets in different geographical locations."""
    assets = [
        Asset(
            id=1,
            asset_id="ASSET-001",
            type="Voting Machine",
            state="South Carolina",
            county="Charleston",
            precinct="Precinct 1",
            location="Charleston Warehouse",
            status="Ready"
        ),
        Asset(
            id=2,
            asset_id="ASSET-002",
            type="Voting Machine",
            state="South Carolina",
            county="Charleston",
            precinct="Precinct 2",
            location="Charleston Warehouse",
            status="Ready"
        ),
        Asset(
            id=3,
            asset_id="ASSET-003",
            type="Voting Machine",
            state="South Carolina",
            county="Greenville",
            precinct="Precinct 1",
            location="Greenville Warehouse",
            status="Ready"
        ),
        Asset(
            id=4,
            asset_id="ASSET-004",
            type="Voting Machine",
            state="North Carolina",
            county="Wake",
            precinct="Precinct 1",
            location="Wake Warehouse",
            status="Ready"
        )
    ]
    
    for asset in assets:
        db_session.add(asset)
    db_session.commit()
    
    return assets

class TestRBACService:
    """Test the RBAC service functionality."""
    
    def test_admin_user_scope(self, db_session, test_users):
        """Test that admin users have unrestricted access."""
        admin_user = test_users['admin']
        rbac = RBACService(db_session, admin_user)
        
        scope = rbac.get_user_scope()
        assert scope['is_admin'] is True
        assert scope['can_view_all'] is True
        assert scope['filters'] == {}
    
    def test_state_user_scope(self, db_session, test_users):
        """Test state-level user scope."""
        state_user = test_users['state_user']
        rbac = RBACService(db_session, state_user)
        
        scope = rbac.get_user_scope()
        assert scope['is_admin'] is False
        assert scope['access_level'] == 'state'
        assert scope['state'] == 'South Carolina'
        assert scope['filters']['state'] == 'South Carolina'
    
    def test_county_user_scope(self, db_session, test_users):
        """Test county-level user scope."""
        county_user = test_users['county_user']
        rbac = RBACService(db_session, county_user)
        
        scope = rbac.get_user_scope()
        assert scope['is_admin'] is False
        assert scope['access_level'] == 'county'
        assert scope['state'] == 'South Carolina'
        assert scope['county'] == 'Charleston'
        assert scope['filters']['state'] == 'South Carolina'
        assert scope['filters']['county'] == 'Charleston'
    
    def test_precinct_user_scope(self, db_session, test_users):
        """Test precinct-level user scope."""
        precinct_user = test_users['precinct_user']
        rbac = RBACService(db_session, precinct_user)
        
        scope = rbac.get_user_scope()
        assert scope['is_admin'] is False
        assert scope['access_level'] == 'precinct'
        assert scope['state'] == 'South Carolina'
        assert scope['county'] == 'Charleston'
        assert scope['precinct'] == 'Precinct 1'
        assert scope['filters']['state'] == 'South Carolina'
        assert scope['filters']['county'] == 'Charleston'
        assert scope['filters']['precinct'] == 'Precinct 1'

class TestAssetAccess:
    """Test asset access control based on user roles."""
    
    def test_admin_can_access_all_assets(self, db_session, test_users, test_assets):
        """Test that admin users can access all assets."""
        admin_user = test_users['admin']
        rbac = RBACService(db_session, admin_user)
        
        query = db_session.query(Asset)
        filtered_query = rbac.apply_asset_filters(query)
        assets = filtered_query.all()
        
        assert len(assets) == 4  # Should see all assets
    
    def test_state_user_asset_access(self, db_session, test_users, test_assets):
        """Test that state users can only access assets in their state."""
        state_user = test_users['state_user']
        rbac = RBACService(db_session, state_user)
        
        query = db_session.query(Asset)
        filtered_query = rbac.apply_asset_filters(query)
        assets = filtered_query.all()
        
        assert len(assets) == 3  # Should see only South Carolina assets
        for asset in assets:
            assert asset.state == 'South Carolina'
    
    def test_county_user_asset_access(self, db_session, test_users, test_assets):
        """Test that county users can only access assets in their county."""
        county_user = test_users['county_user']
        rbac = RBACService(db_session, county_user)
        
        query = db_session.query(Asset)
        filtered_query = rbac.apply_asset_filters(query)
        assets = filtered_query.all()
        
        assert len(assets) == 2  # Should see only Charleston county assets
        for asset in assets:
            assert asset.state == 'South Carolina'
            assert asset.county == 'Charleston'
    
    def test_precinct_user_asset_access(self, db_session, test_users, test_assets):
        """Test that precinct users can only access assets in their precinct."""
        precinct_user = test_users['precinct_user']
        rbac = RBACService(db_session, precinct_user)
        
        query = db_session.query(Asset)
        filtered_query = rbac.apply_asset_filters(query)
        assets = filtered_query.all()
        
        assert len(assets) == 1  # Should see only Precinct 1 assets
        asset = assets[0]
        assert asset.state == 'South Carolina'
        assert asset.county == 'Charleston'
        assert asset.precinct == 'Precinct 1'

class TestAssetCreation:
    """Test asset creation permissions."""
    
    def test_user_can_create_asset_in_scope(self, db_session, test_users):
        """Test that users can create assets within their geographical scope."""
        county_user = test_users['county_user']
        rbac = RBACService(db_session, county_user)
        
        asset_data = {
            'state': 'South Carolina',
            'county': 'Charleston',
            'precinct': 'Precinct 1'
        }
        
        assert rbac.can_create_asset(asset_data) is True
    
    def test_user_cannot_create_asset_outside_scope(self, db_session, test_users):
        """Test that users cannot create assets outside their geographical scope."""
        county_user = test_users['county_user']
        rbac = RBACService(db_session, county_user)
        
        asset_data = {
            'state': 'South Carolina',
            'county': 'Greenville',  # Different county
            'precinct': 'Precinct 1'
        }
        
        assert rbac.can_create_asset(asset_data) is False

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
