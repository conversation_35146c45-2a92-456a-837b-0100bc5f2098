from sqlalchemy import Column, String, Integer, DateTime, Text, JSON
from sqlalchemy.sql import func
from app.config.database import Base
from datetime import datetime

class PackingList(Base):
    __tablename__ = "packing_lists"

    id = Column(String(255), primary_key=True)
    election = Column(String(255), nullable=False)
    to = Column(String(255), nullable=False)  # destination
    from_location = Column("from_location", String(255), nullable=False)  # source
    packing_date = Column(DateTime, nullable=True)
    packing_user = Column(String(255), nullable=True)
    supply_package = Column(String(255), nullable=False)
    item = Column(String(255), nullable=False)
    qty = Column(Integer, nullable=False, default=1)
    proofed_by = Column(String(255), nullable=True)
    verification_status = Column(String(50), nullable=False, default='pending')  # pending, verified, rejected
    delivery_date = Column(DateTime, nullable=True)
    delivery_method = Column(String(50), nullable=True)  # pickup, delivery, courier, transport
    contact_person = Column(String(255), nullable=True)
    unpacked_by = Column(String(255), nullable=True)
    status = Column(String(50), nullable=False, default='packed')  # packed, verified, in_transit, delivered, unpacked
    
    # JSON fields for complex data structures
    verification_items = Column(JSON, nullable=True, default=list)
    completion_status = Column(JSON, nullable=True, default=dict)
    unpack_items = Column(JSON, nullable=True, default=list)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def get_verification_items_data(self):
        """Get verification items as list."""
        return self.verification_items if self.verification_items else []

    def set_verification_items_data(self, items):
        """Set verification items."""
        self.verification_items = items

    def get_completion_status_data(self):
        """Get completion status as dict."""
        return self.completion_status if self.completion_status else {}

    def set_completion_status_data(self, status):
        """Set completion status."""
        self.completion_status = status

    def get_unpack_items_data(self):
        """Get unpack items as list."""
        return self.unpack_items if self.unpack_items else []

    def set_unpack_items_data(self, items):
        """Set unpack items."""
        self.unpack_items = items

    def to_dict(self) -> dict:
        """Convert packing list object to dictionary."""
        return {
            "id": self.id,
            "election": self.election,
            "to": self.to,
            "from": self.from_location,
            "packingDate": self.packing_date.isoformat() if self.packing_date else None,
            "packingUser": self.packing_user,
            "supplyPackage": self.supply_package,
            "item": self.item,
            "qty": self.qty,
            "proofedBy": self.proofed_by,
            "verificationStatus": self.verification_status,
            "deliveryDate": self.delivery_date.isoformat() if self.delivery_date else None,
            "deliveryMethod": self.delivery_method,
            "contactPerson": self.contact_person,
            "unpackedBy": self.unpacked_by,
            "status": self.status,
            "verificationItems": self.get_verification_items_data(),
            "completionStatus": self.get_completion_status_data(),
            "unpackItems": self.get_unpack_items_data(),
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None
        } 