// src/services/assetTransferService.ts
// Frontend API service for Asset Transfer Management

import {
  AssetTransfer,
  AssetTransferCreate,
  AssetTransferUpdate,
  AssetTransferFilters,
  TransferType,
  TransferStatus,
  ApiResponse,
  PaginatedResponse
} from '../types/workflow';

// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class AssetTransferService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/asset-transfers${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || errorData?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Asset Transfer API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Get all asset transfers with filters and pagination
  async getAssetTransfers(filters: AssetTransferFilters = {}): Promise<PaginatedResponse<AssetTransfer>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const endpoint = params.toString() ? `?${params.toString()}` : '';
    return this.makeRequest<PaginatedResponse<AssetTransfer>>(endpoint);
  }

  // Get a specific asset transfer by ID
  async getAssetTransfer(id: number): Promise<ApiResponse<AssetTransfer>> {
    return this.makeRequest<ApiResponse<AssetTransfer>>(`/${id}`);
  }

  // Create a new asset transfer
  async createAssetTransfer(transferData: AssetTransferCreate): Promise<ApiResponse<AssetTransfer>> {
    return this.makeRequest<ApiResponse<AssetTransfer>>('', {
      method: 'POST',
      body: JSON.stringify(transferData),
    });
  }

  // Update an existing asset transfer
  async updateAssetTransfer(id: number, updateData: AssetTransferUpdate): Promise<ApiResponse<AssetTransfer>> {
    return this.makeRequest<ApiResponse<AssetTransfer>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  // Delete an asset transfer
  async deleteAssetTransfer(id: number): Promise<ApiResponse<null>> {
    return this.makeRequest<ApiResponse<null>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  // Get transfers for a specific asset
  async getTransfersForAsset(assetId: number): Promise<ApiResponse<AssetTransfer[]>> {
    return this.makeRequest<ApiResponse<AssetTransfer[]>>(`/asset/${assetId}`);
  }

  // Update transfer status
  async updateTransferStatus(
    id: number, 
    status: TransferStatus, 
    notes?: string
  ): Promise<ApiResponse<AssetTransfer>> {
    return this.updateAssetTransfer(id, { status, notes });
  }

  // Mark transfer as in transit
  async markInTransit(
    id: number, 
    trackingNumber?: string, 
    carrier?: string
  ): Promise<ApiResponse<AssetTransfer>> {
    return this.updateAssetTransfer(id, {
      status: TransferStatus.IN_TRANSIT,
      trackingNumber,
      carrier,
    });
  }

  // Mark transfer as delivered
  async markDelivered(
    id: number, 
    actualDeliveryDate: string,
    conditionAfterTransfer?: string,
    notes?: string
  ): Promise<ApiResponse<AssetTransfer>> {
    return this.updateAssetTransfer(id, {
      status: TransferStatus.DELIVERED,
      actualDeliveryDate,
      conditionAfterTransfer,
      notes,
    });
  }

  // Verify transfer with verification code
  async verifyTransfer(id: number, verificationCode: string): Promise<ApiResponse<AssetTransfer>> {
    return this.makeRequest<ApiResponse<AssetTransfer>>(`/${id}/verify`, {
      method: 'POST',
      body: JSON.stringify({ verificationCode }),
    });
  }

  // Cancel transfer
  async cancelTransfer(id: number, reason?: string): Promise<ApiResponse<AssetTransfer>> {
    return this.updateAssetTransfer(id, {
      status: TransferStatus.CANCELLED,
      notes: reason,
    });
  }

  // Get transfer statistics
  async getTransferStats(
    startDate?: string,
    endDate?: string,
    transferType?: TransferType
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (transferType) params.append('transfer_type', transferType);

    const endpoint = params.toString() ? `/stats?${params.toString()}` : '/stats';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }

  // Get pending transfers
  async getPendingTransfers(): Promise<ApiResponse<AssetTransfer[]>> {
    return this.getAssetTransfers({
      status: TransferStatus.INITIATED,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get overdue transfers
  async getOverdueTransfers(): Promise<ApiResponse<AssetTransfer[]>> {
    return this.makeRequest<ApiResponse<AssetTransfer[]>>('/overdue');
  }

  // Bulk update transfers
  async bulkUpdateTransfers(
    transferIds: number[],
    updateData: Partial<AssetTransferUpdate>
  ): Promise<ApiResponse<AssetTransfer[]>> {
    return this.makeRequest<ApiResponse<AssetTransfer[]>>('/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({
        transferIds,
        updateData,
      }),
    });
  }

  // Get transfer history for reporting
  async getTransferHistory(
    startDate: string,
    endDate: string,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
      format,
    });

    return this.makeRequest<any>(`/history?${params.toString()}`);
  }

  // Generate transfer documentation
  async generateTransferDocument(id: number, template: string = 'default'): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>(`/${id}/document`, {
      method: 'POST',
      body: JSON.stringify({ template }),
    });
  }
}

export const assetTransferService = new AssetTransferService();
export default assetTransferService;
