import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileDown, Edit, Trash, Eye, AlertTriangle } from "lucide-react";
import packingListService, { PackingList, PackingListCreationData } from "@/services/packingListService";
import { exportData } from "@/utils/exportUtils";
import { ExportOptions } from "@/components/ExportOptions";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function PackingListPage() {
  const [packingLists, setPackingLists] = useState<PackingList[]>([]);
  const [filteredPackingLists, setFilteredPackingLists] = useState<PackingList[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPackingList, setSelectedPackingList] = useState<PackingList | undefined>(undefined);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load packing lists from backend
  useEffect(() => {
    loadPackingLists();
  }, []);

  // Filter packing lists when search query changes
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredPackingLists(packingLists);
    } else {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = packingLists.filter(
        (packingList) =>
          packingList.election?.toLowerCase().includes(lowercaseQuery) ||
          packingList.item?.toLowerCase().includes(lowercaseQuery) ||
          packingList.to?.toLowerCase().includes(lowercaseQuery) ||
          packingList.from?.toLowerCase().includes(lowercaseQuery)
      );
      setFilteredPackingLists(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, packingLists]);

  const loadPackingLists = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await packingListService.getAllPackingLists();
      setPackingLists(result.packingLists || []);
      setFilteredPackingLists(result.packingLists || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load packing lists');
      console.error('Error loading packing lists:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new packing list
  const handleAddPackingList = async (packingListData: PackingListCreationData) => {
    try {
      const newPackingList = await packingListService.createPackingList(packingListData);
      setPackingLists([...packingLists, newPackingList]);
      setShowAddDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add packing list');
      console.error('Error adding packing list:', err);
    }
  };

  // Handle editing a packing list
  const handleEditPackingList = async (packingListData: PackingListCreationData) => {
    if (selectedPackingList) {
      try {
        const updatedPackingList = await packingListService.updatePackingList(
          selectedPackingList.id, 
          packingListData
        );

        setPackingLists(packingLists.map(pl =>
          pl.id === updatedPackingList.id ? updatedPackingList : pl
        ));
        setShowEditDialog(false);
        setSelectedPackingList(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update packing list');
        console.error('Error updating packing list:', err);
      }
    }
  };

  // Handle viewing a packing list
  const handleViewPackingList = (packingList: PackingList) => {
    setSelectedPackingList(packingList);
    setShowViewDialog(true);
  };

  // Handle deleting a packing list
  const handleDeletePackingList = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this packing list?")) {
      try {
        const success = await packingListService.deletePackingList(id);
        if (success) {
          setPackingLists(packingLists.filter(pl => pl.id !== id));
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete packing list');
        console.error('Error deleting packing list:', err);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = packingLists.map(pl => ({
      'ID': pl.id,
      'Election': pl.election || 'N/A',
      'Item': pl.item || 'N/A',
      'From': pl.from || 'N/A',
      'To': pl.to || 'N/A',
      'Status': pl.status || 'N/A',
      'Created At': new Date(pl.createdAt).toLocaleDateString()
    }));

    exportData(
      exportableData,
      format,
      'Packing_Lists_Export',
      'Packing Lists',
      ['ID', 'Election', 'Item', 'From', 'To', 'Status', 'Created At']
    );
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPackingLists.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPackingLists.length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading packing lists...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600">{error}</p>
              <Button onClick={loadPackingLists} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Packing Lists</h1>
          <div className="flex gap-2">
            <ExportOptions onExport={handleExport} />
            <Button onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        <div className="flex items-center mb-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search packing lists..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Election</TableHead>
                  <TableHead>Item</TableHead>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.map((packingList) => (
                  <TableRow key={packingList.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleViewPackingList(packingList)}>
                    <TableCell className="font-medium">{packingList.election || 'N/A'}</TableCell>
                    <TableCell>{packingList.item || 'N/A'}</TableCell>
                    <TableCell>{packingList.from || 'N/A'}</TableCell>
                    <TableCell>{packingList.to || 'N/A'}</TableCell>
                    <TableCell>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        packingList.status === 'delivered' || packingList.status === 'verified' ? 'bg-green-100 text-green-800' : 
                        packingList.status === 'in_transit' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {packingList.status || 'Unknown'}
                      </span>
                    </TableCell>
                    <TableCell>{new Date(packingList.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm" onClick={() => handleViewPackingList(packingList)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => {
                          setSelectedPackingList(packingList);
                          setShowEditDialog(true);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDeletePackingList(packingList.id)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {currentItems.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      No packing lists found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() => paginate(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Add Dialog */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Add New Packing List</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="election" className="text-right">Election *</Label>
                <Input
                  id="election"
                  className="col-span-3"
                  placeholder="Enter election name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="item" className="text-right">Item *</Label>
                <Input
                  id="item"
                  className="col-span-3"
                  placeholder="Enter item name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="from" className="text-right">From *</Label>
                <Input
                  id="from"
                  className="col-span-3"
                  placeholder="Enter source location"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="to" className="text-right">To *</Label>
                <Input
                  id="to"
                  className="col-span-3"
                  placeholder="Enter destination location"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="qty" className="text-right">Quantity</Label>
                <Input
                  id="qty"
                  type="number"
                  className="col-span-3"
                  placeholder="Enter quantity"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">Status</Label>
                <Select>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="packed">Packed</SelectItem>
                    <SelectItem value="verified">Verified</SelectItem>
                    <SelectItem value="in_transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="unpacked">Unpacked</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>Cancel</Button>
              <Button onClick={() => {
                const election = (document.getElementById('election') as HTMLInputElement)?.value;
                const item = (document.getElementById('item') as HTMLInputElement)?.value;
                const from = (document.getElementById('from') as HTMLInputElement)?.value;
                const to = (document.getElementById('to') as HTMLInputElement)?.value;
                const qty = parseInt((document.getElementById('qty') as HTMLInputElement)?.value) || 1;
                
                if (election && item && from && to) {
                  handleAddPackingList({
                    election,
                    item,
                    from,
                    to,
                    qty,
                    supplyPackage: 'Default Package',
                    status: 'packed'
                  });
                }
              }}>
                Add Packing List
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Edit Packing List</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-election" className="text-right">Election *</Label>
                <Input
                  id="edit-election"
                  className="col-span-3"
                  defaultValue={selectedPackingList?.election || ''}
                  placeholder="Enter election name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-item" className="text-right">Item *</Label>
                <Input
                  id="edit-item"
                  className="col-span-3"
                  defaultValue={selectedPackingList?.item || ''}
                  placeholder="Enter item name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-from" className="text-right">From *</Label>
                <Input
                  id="edit-from"
                  className="col-span-3"
                  defaultValue={selectedPackingList?.from || ''}
                  placeholder="Enter source location"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-to" className="text-right">To *</Label>
                <Input
                  id="edit-to"
                  className="col-span-3"
                  defaultValue={selectedPackingList?.to || ''}
                  placeholder="Enter destination location"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-qty" className="text-right">Quantity</Label>
                <Input
                  id="edit-qty"
                  type="number"
                  className="col-span-3"
                  defaultValue={selectedPackingList?.qty || 1}
                  placeholder="Enter quantity"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>Cancel</Button>
              <Button onClick={() => {
                const election = (document.getElementById('edit-election') as HTMLInputElement)?.value;
                const item = (document.getElementById('edit-item') as HTMLInputElement)?.value;
                const from = (document.getElementById('edit-from') as HTMLInputElement)?.value;
                const to = (document.getElementById('edit-to') as HTMLInputElement)?.value;
                const qty = parseInt((document.getElementById('edit-qty') as HTMLInputElement)?.value) || 1;
                
                if (election && item && from && to) {
                  handleEditPackingList({
                    election,
                    item,
                    from,
                    to,
                    qty,
                    supplyPackage: 'Default Package'
                  });
                }
              }}>
                Update Packing List
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Dialog */}
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Packing List Details</DialogTitle>
            </DialogHeader>
            {selectedPackingList && (
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">Election:</Label>
                  <div className="col-span-3">{selectedPackingList.election}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">Item:</Label>
                  <div className="col-span-3">{selectedPackingList.item}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">From:</Label>
                  <div className="col-span-3">{selectedPackingList.from}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">To:</Label>
                  <div className="col-span-3">{selectedPackingList.to}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">Quantity:</Label>
                  <div className="col-span-3">{selectedPackingList.qty}</div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">Status:</Label>
                  <div className="col-span-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedPackingList.status === 'delivered' || selectedPackingList.status === 'verified' ? 'bg-green-100 text-green-800' : 
                      selectedPackingList.status === 'in_transit' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedPackingList.status}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right font-medium">Created:</Label>
                  <div className="col-span-3">{new Date(selectedPackingList.createdAt).toLocaleDateString()}</div>
                </div>
              </div>
            )}
            <DialogFooter>
              <Button onClick={() => setShowViewDialog(false)}>Close</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}
