# app/routes/auth.py - FIXED VERSION TO MATCH FRONTEND
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.config.database import get_db
from app.models.user import User
from app.schemas.user import UserC<PERSON>, UserLogin, TokenResponse, UserProfile, PasswordChange
from app.middleware.auth import get_current_user
from uuid import uuid4
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/login")
async def login_user(login_data: dict, db: Session = Depends(get_db)):
    """
    Login user with email/username/loginId and password.
    Fixed to match frontend expectations.
    """
    try:
        # Extract credentials from request
        email_or_username = login_data.get("email", "").strip()
        password = login_data.get("password", "")
        
        logger.info(f"🔐 Login attempt for: {email_or_username}")
        
        if not email_or_username or not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email and password are required"
            )
        
        # Find user by email, username, or login_id (flexible login)
        user = db.query(User).filter(
            or_(
                User.email == email_or_username.lower(),
                User.username == email_or_username,
                User.login_id == email_or_username
            )
        ).first()
        
        if not user:
            logger.warning(f"❌ User not found: {email_or_username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Check if user is active
        if not user.status or not user.login_enabled:
            logger.warning(f"❌ Inactive user: {email_or_username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is inactive"
            )
        
        # Verify password
        if not user.verify_password(password):
            logger.warning(f"❌ Invalid password for: {email_or_username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
        
        # Generate token
        token = user.generate_token()
        
        # Create user data in the format your frontend expects
        user_data = {
            "id": user.id,
            "firstName": user.first_name,
            "lastName": user.last_name,
            "fullName": user.get_full_name(),
            "email": user.email,
            "role": user.role,
            "department": user.department,
            "location": user.location,
            "accessLevel": user.access_level,
            "county": user.county,
            "precinct": user.precinct,
            "userGroup": user.user_group,
            "loginId": user.login_id,
            "permissions": {
                "isAdmin": user.is_admin(),
                "hasStateAccess": user.has_state_access(),
                "hasCountyAccess": user.has_county_access(),
                "hasPrecinctAccess": user.has_precinct_access(),
                "canManageUsers": user.is_admin(),
                "canManageAssets": user.is_admin(),
                "canViewReports": True
            },
            "defaultRoute": get_default_route(user)
        }
        
        # Return response in the exact format your frontend expects
        response = {
            "success": True,
            "message": "Login successful",
            "data": {
                "user": user_data,
                "token": token,
                "redirectTo": get_default_route(user)
            }
        }
        
        logger.info(f"✅ Login successful for: {user.get_full_name()} ({user.role})")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )

def get_default_route(user: User) -> str:
    """Get appropriate dashboard route based on user role and access level."""
    # FOR NOW: Always redirect to /dashboard regardless of role
    # This matches your frontend logic
    return '/dashboard'
    
    # FUTURE: Uncomment when role-specific dashboards are ready
    """
    switch (user.role) {
      case 'Portal Admin':
      case 'admin':
        return '/admin/dashboard'
      case 'manager':
        return '/state/dashboard' if user.access_level == 'state' else '/county/dashboard'
      case 'user':
        if user.access_level == 'state':
          return '/state/dashboard'
        elif user.access_level == 'county':
          return '/county/dashboard'
        elif user.access_level == 'precinct':
          return '/precinct/dashboard'
        else:
          return '/dashboard'
      default:
        return '/dashboard'
    }
    """

@router.post("/register")
async def register_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Register a new user.
    """
    try:
        # Check if user already exists by email or login_id
        existing_user = db.query(User).filter(
            or_(User.email == user_data.email, User.login_id == user_data.login_id)
        ).first()
        
        if existing_user:
            if existing_user.email == user_data.email:
                return {
                    "success": False,
                    "message": "User with this email already exists"
                }
            else:
                return {
                    "success": False,
                    "message": "User with this login ID already exists"
                }
        
        # Create new user
        new_user = User(
            id=str(uuid4()),
            first_name=user_data.first_name.strip(),
            last_name=user_data.last_name.strip(),
            email=user_data.email.lower(),
            mobile=user_data.mobile.strip(),
            user_group=user_data.user_group.strip(),
            login_enabled=user_data.login_enabled,
            login_id=user_data.login_id.strip(),
            access_level=user_data.access_level,
            county=user_data.county.strip() if user_data.county else None,
            precinct=user_data.precinct.strip() if user_data.precinct else None,
            status=user_data.status,
            image=user_data.image,
            company=user_data.company.strip() if user_data.company else None,
            employee_no=user_data.employee_no.strip() if user_data.employee_no else None,
            manager=user_data.manager.strip() if user_data.manager else None,
            department=user_data.department.strip() if user_data.department else None,
            location=user_data.location.strip() if user_data.location else None,
            address_line1=user_data.address_line1.strip() if user_data.address_line1 else None,
            address_line2=user_data.address_line2.strip() if user_data.address_line2 else None,
            city=user_data.city.strip() if user_data.city else None,
            state=user_data.state.strip() if user_data.state else None,
            pincode=user_data.pincode.strip() if user_data.pincode else None,
            country=user_data.country.strip() if user_data.country else None,
            role=user_data.role,
            username=user_data.username.strip() if user_data.username else None
        )
        
        # Hash password
        new_user.set_password(user_data.password)
        
        # Save to database
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        # Generate token
        token = new_user.generate_token()
        
        # Create user data
        user_response_data = {
            "id": new_user.id,
            "firstName": new_user.first_name,
            "lastName": new_user.last_name,
            "fullName": new_user.get_full_name(),
            "email": new_user.email,
            "role": new_user.role,
            "department": new_user.department,
            "location": new_user.location,
            "accessLevel": new_user.access_level,
            "county": new_user.county,
            "precinct": new_user.precinct,
            "userGroup": new_user.user_group,
            "loginId": new_user.login_id,
            "permissions": {
                "isAdmin": new_user.is_admin(),
                "hasStateAccess": new_user.has_state_access(),
                "hasCountyAccess": new_user.has_county_access(),
                "hasPrecinctAccess": new_user.has_precinct_access(),
                "canManageUsers": new_user.is_admin(),
                "canManageAssets": new_user.is_admin(),
                "canViewReports": True
            },
            "defaultRoute": get_default_route(new_user)
        }
        
        return {
            "success": True,
            "message": "User registered successfully",
            "data": {
                "user": user_response_data,
                "token": token
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        db.rollback()
        return {
            "success": False,
            "message": "Internal server error during registration"
        }

@router.get("/profile")
async def get_user_profile(current_user: User = Depends(get_current_user)):
    """
    Get current user profile.
    """
    try:
        user_data = {
            "id": current_user.id,
            "firstName": current_user.first_name,
            "lastName": current_user.last_name,
            "fullName": current_user.get_full_name(),
            "email": current_user.email,
            "role": current_user.role,
            "department": current_user.department,
            "location": current_user.location,
            "accessLevel": current_user.access_level,
            "county": current_user.county,
            "precinct": current_user.precinct,
            "userGroup": current_user.user_group,
            "loginId": current_user.login_id,
            "mobile": current_user.mobile,
            "image": current_user.image,
            "company": current_user.company,
            "permissions": {
                "isAdmin": current_user.is_admin(),
                "hasStateAccess": current_user.has_state_access(),
                "hasCountyAccess": current_user.has_county_access(),
                "hasPrecinctAccess": current_user.has_precinct_access(),
                "canManageUsers": current_user.is_admin(),
                "canManageAssets": current_user.is_admin(),
                "canViewReports": True
            }
        }
        
        return {
            "success": True,
            "data": {
                "user": user_data
            }
        }
    except Exception as e:
        logger.error(f"Profile fetch error: {e}")
        return {
            "success": False,
            "message": "Error fetching profile"
        }

@router.put("/profile")
async def update_user_profile(
    profile_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile.
    """
    try:
        # Update allowed fields
        allowed_fields = [
            'first_name', 'last_name', 'mobile', 'image', 'company',
            'employee_no', 'department', 'location', 'address_line1',
            'address_line2', 'city', 'state', 'pincode', 'country', 'username'
        ]
        
        update_data = {}
        for field, value in profile_data.items():
            if field in allowed_fields and value is not None:
                if isinstance(value, str):
                    update_data[field] = value.strip()
                else:
                    update_data[field] = value
        
        # Update user
        for field, value in update_data.items():
            setattr(current_user, field, value)
        
        db.commit()
        db.refresh(current_user)
        
        user_data = {
            "id": current_user.id,
            "firstName": current_user.first_name,
            "lastName": current_user.last_name,
            "fullName": current_user.get_full_name(),
            "email": current_user.email,
            "role": current_user.role,
            "department": current_user.department,
            "location": current_user.location,
            "accessLevel": current_user.access_level,
            "county": current_user.county,
            "precinct": current_user.precinct,
            "userGroup": current_user.user_group,
            "loginId": current_user.login_id
        }
        
        return {
            "success": True,
            "message": "Profile updated successfully",
            "data": {
                "user": user_data
            }
        }
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        db.rollback()
        return {
            "success": False,
            "message": "Error updating profile"
        }

@router.post("/change-password")
async def change_password(
    password_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Change user password.
    """
    try:
        current_password = password_data.get("currentPassword")
        new_password = password_data.get("newPassword")
        
        if not current_password or not new_password:
            return {
                "success": False,
                "message": "Current password and new password are required"
            }
        
        # Verify current password
        if not current_user.verify_password(current_password):
            return {
                "success": False,
                "message": "Current password is incorrect"
            }
        
        if len(new_password) < 6:
            return {
                "success": False,
                "message": "Password must be at least 6 characters long"
            }
        
        # Set new password
        current_user.set_password(new_password)
        
        db.commit()
        
        return {
            "success": True,
            "message": "Password changed successfully"
        }
        
    except Exception as e:
        logger.error(f"Password change error: {e}")
        db.rollback()
        return {
            "success": False,
            "message": "Error changing password"
        }

@router.post("/logout")
async def logout_user(current_user: User = Depends(get_current_user)):
    """
    Logout user (token invalidation would be handled on client side).
    """
    return {
        "success": True,
        "message": "Logged out successfully"
    }

@router.get("/verify-token")
async def verify_token(current_user: User = Depends(get_current_user)):
    """
    Verify if token is valid and return user info.
    """
    return {
        "success": True,
        "valid": True,
        "data": {
            "user": {
                "id": current_user.id,
                "email": current_user.email,
                "role": current_user.role,
                "accessLevel": current_user.access_level,
                "fullName": current_user.get_full_name()
            }
        }
    }

# Health check for auth system
@router.get("/health")
async def auth_health_check():
    """Auth system health check."""
    return {
        "success": True,
        "message": "Auth system is healthy",
        "timestamp": "2025-06-29T10:20:00Z"
    }

# User Management Endpoints - Extended auth functionality
@router.get("/users")
async def get_all_users(
    skip: int = 0,
    limit: int = 100,
    search: str = None,
    user_group: str = None,
    status: bool = None,
    access_level: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get all users with optional filtering.
    Frontend expects this endpoint for user management.
    """
    try:
        # Build query
        query = db.query(User)
        
        # Apply filters based on current user's access level
        if current_user.access_level == 'county' and current_user.county:
            # County users can only see users from their county
            query = query.filter(User.county == current_user.county)
        elif current_user.role not in ['Portal Admin', 'admin']:
            # Non-admin users have limited access
            query = query.filter(User.id == current_user.id)
        
        # Apply search filters
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(
                or_(
                    User.first_name.ilike(search_pattern),
                    User.last_name.ilike(search_pattern),
                    User.email.ilike(search_pattern),
                    User.login_id.ilike(search_pattern)
                )
            )
        
        if user_group:
            query = query.filter(User.user_group == user_group)
            
        if status is not None:
            query = query.filter(User.status == status)
            
        if access_level:
            query = query.filter(User.access_level == access_level)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        users = query.offset(skip).limit(limit).all()
        
        # Format response to match frontend expectations
        user_list = []
        for user in users:
            user_dict = {
                "id": user.id,
                "firstName": user.first_name,
                "lastName": user.last_name,
                "email": user.email,
                "mobile": user.mobile,
                "userGroup": user.user_group,
                "loginEnabled": user.login_enabled,
                "loginId": user.login_id,
                "accessLevel": user.access_level,
                "county": user.county,
                "precinct": getattr(user, 'precinct', None),
                "status": user.status,
                "image": user.image,
                "company": user.company,
                "department": user.department,
                "location": user.location,
                "role": user.role,
                "username": user.username,
                "phone": getattr(user, 'phone', user.mobile),
                "createdAt": user.created_at.isoformat() if user.created_at else None,
                "updatedAt": user.updated_at.isoformat() if user.updated_at else None
            }
            user_list.append(user_dict)
        
        logger.info(f"Retrieved {len(users)} users for user {current_user.email}")
        
        return {
            "success": True,
            "data": {
                "users": user_list,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.error(f"Error retrieving users: {e}")
        return {
            "success": False,
            "message": f"Failed to retrieve users: {str(e)}"
        }

@router.post("/register")
async def register_user(
    user_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Register a new user. Frontend expects this endpoint.
    Requires admin access.
    """
    try:
        # Check admin access
        if current_user.role not in ['Portal Admin', 'admin']:
            return {
                "success": False,
                "message": "Not authorized to create users"
            }
        
        # Check if user already exists
        existing_user = db.query(User).filter(
            or_(
                User.email == user_data.get("email", "").lower(),
                User.login_id == user_data.get("loginId", "")
            )
        ).first()
        
        if existing_user:
            return {
                "success": False,
                "message": "User with this email or login ID already exists"
            }
        
        # Validate geographical scope based on access level
        access_level = user_data.get("accessLevel", "county")
        state = user_data.get("state")
        county = user_data.get("county")
        precinct = user_data.get("precinct")

        # RBAC validation
        if access_level == "state" and not state:
            return {
                "success": False,
                "message": "State is required for state-level access"
            }
        elif access_level == "county" and (not state or not county):
            return {
                "success": False,
                "message": "State and county are required for county-level access"
            }
        elif access_level == "precinct" and (not state or not county or not precinct):
            return {
                "success": False,
                "message": "State, county, and precinct are required for precinct-level access"
            }

        # Create new user
        new_user = User(
            id=str(uuid4()),
            first_name=user_data.get("firstName", ""),
            last_name=user_data.get("lastName", ""),
            email=user_data.get("email", "").lower(),
            mobile=user_data.get("mobile", ""),
            user_group=user_data.get("userGroup", "Default Group"),
            login_enabled=user_data.get("loginEnabled", True),
            login_id=user_data.get("loginId", ""),
            access_level=access_level,
            state=state,
            county=county,
            precinct=precinct,
            status=user_data.get("status", True),
            image=user_data.get("image"),
            company=user_data.get("company"),
            department=user_data.get("department"),
            location=user_data.get("location"),
            role=user_data.get("role", "user"),
            username=user_data.get("username")
        )
        
        # Set password
        if "password" in user_data:
            new_user.set_password(user_data["password"])
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        logger.info(f"User {new_user.email} created by {current_user.email}")
        
        return {
            "success": True,
            "message": "User created successfully",
            "data": {
                "user": {
                    "id": new_user.id,
                    "email": new_user.email,
                    "firstName": new_user.first_name,
                    "lastName": new_user.last_name
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        db.rollback()
        return {
            "success": False,
            "message": f"Failed to create user: {str(e)}"
        }

@router.put("/users/{user_id}")
async def update_user(
    user_id: str,
    user_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update a user. Frontend expects this endpoint.
    Users can update themselves, admins can update anyone.
    """
    try:
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            return {
                "success": False,
                "message": "User not found"
            }
        
        # Check permissions
        is_admin = current_user.role in ['Portal Admin', 'admin']
        is_self = current_user.id == user_id
        
        if not (is_admin or is_self):
            return {
                "success": False,
                "message": "Not authorized to update this user"
            }
        
        # Update user fields
        updatable_fields = [
            'first_name', 'last_name', 'mobile', 'phone', 'image', 'company',
            'department', 'location', 'user_group', 'login_enabled', 'login_id',
            'access_level', 'county', 'status', 'role', 'username'
        ]
        
        for field, value in user_data.items():
            # Convert camelCase to snake_case for database fields
            db_field = field
            if field == 'firstName':
                db_field = 'first_name'
            elif field == 'lastName':
                db_field = 'last_name'
            elif field == 'userGroup':
                db_field = 'user_group'
            elif field == 'loginEnabled':
                db_field = 'login_enabled'
            elif field == 'loginId':
                db_field = 'login_id'
            elif field == 'accessLevel':
                db_field = 'access_level'
            
            # Only allow certain fields for non-admins
            if not is_admin and db_field not in ['first_name', 'last_name', 'mobile', 'phone', 'image']:
                continue
                
            if db_field in updatable_fields and hasattr(user, db_field):
                setattr(user, db_field, value)
        
        # Handle password update
        if "password" in user_data and user_data["password"]:
            user.set_password(user_data["password"])
        
        user.updated_at = user.get_current_time()
        
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {user.email} updated by {current_user.email}")
        
        return {
            "success": True,
            "message": "User updated successfully"
        }
        
    except Exception as e:
        logger.error(f"Error updating user {user_id}: {e}")
        db.rollback()
        return {
            "success": False,
            "message": f"Failed to update user: {str(e)}"
        }