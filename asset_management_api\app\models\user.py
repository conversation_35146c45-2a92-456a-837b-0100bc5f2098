# app/models/user.py - NO ENUMS VERSION (GUARANTEED TO WORK)
from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.sql import func
from app.config.database import Base
from passlib.context import CryptContext
from jose import jwt
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    __tablename__ = "users"

    id = Column(String(36), primary_key=True)
    first_name = Column(String(255), nullable=False)
    last_name = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False, unique=True, index=True)
    password = Column(String(255), nullable=False)
    mobile = Column(String(20), nullable=False)
    user_group = Column(String(100), nullable=False)
    login_enabled = Column(Boolean, default=True)
    login_id = Column(String(100), nullable=False, unique=True)
    # RBAC fields - Enhanced for hierarchical access control
    access_level = Column(String(20), nullable=False, index=True)  # state/county/precinct
    state = Column(String(100), nullable=True, index=True)  # Required for county/precinct users
    county = Column(String(100), nullable=True, index=True)  # Required for county/precinct users
    precinct = Column(String(100), nullable=True, index=True)  # Required for precinct users
    status = Column(Boolean, default=True)
    image = Column(String(500), nullable=True)
    company = Column(String(255), nullable=True)
    employee_no = Column(String(50), nullable=True)
    manager = Column(String(255), nullable=True)
    department = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    pincode = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    role = Column(String(50), nullable=False, default="user")  # Simple string field
    username = Column(String(100), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def verify_password(self, password: str) -> bool:
        """Verify password against hashed password."""
        return pwd_context.verify(password, self.password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash password."""
        return pwd_context.hash(password)

    def set_password(self, password: str):
        """Set password with hashing."""
        self.password = self.get_password_hash(password)

    def generate_token(self) -> str:
        """Generate JWT token with enhanced data."""
        payload = {
            "id": self.id,
            "email": self.email,
            "role": self.role,
            "access_level": self.access_level,
            "county": self.county,
            "precinct": self.precinct,
            "user_group": self.user_group,
            "login_id": self.login_id,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "exp": datetime.utcnow() + timedelta(hours=24)
        }
        
        secret_key = os.getenv("JWT_SECRET", "your-secret-key")
        return jwt.encode(payload, secret_key, algorithm="HS256")

    def get_full_name(self) -> str:
        """Get full name."""
        return f"{self.first_name} {self.last_name}"

    def is_admin(self) -> bool:
        """Check if user has admin privileges."""
        return self.role in ["admin", "Portal Admin"]

    def validate_geographical_scope(self) -> bool:
        """Validate that user has proper geographical scope based on access level."""
        if self.access_level == "state":
            return self.state is not None
        elif self.access_level == "county":
            return self.state is not None and self.county is not None
        elif self.access_level == "precinct":
            return self.state is not None and self.county is not None and self.precinct is not None
        return False

    def can_access_asset(self, asset_state: str, asset_county: str = None, asset_precinct: str = None) -> bool:
        """Check if user can access an asset based on geographical hierarchy."""
        if self.is_admin():
            return True

        if self.access_level == "state":
            return self.state == asset_state
        elif self.access_level == "county":
            return self.state == asset_state and self.county == asset_county
        elif self.access_level == "precinct":
            return (self.state == asset_state and
                   self.county == asset_county and
                   self.precinct == asset_precinct)
        return False

    def get_asset_query_filters(self) -> dict:
        """Get query filters for assets based on user's geographical scope."""
        if self.is_admin():
            return {}  # No filters for admin

        filters = {"state": self.state}

        if self.access_level in ["county", "precinct"]:
            filters["county"] = self.county

        if self.access_level == "precinct":
            filters["precinct"] = self.precinct

        return filters

    def has_state_access(self) -> bool:
        """Check if user has state access."""
        return self.access_level == "state" or self.is_admin()

    def has_county_access(self) -> bool:
        """Check if user has county access."""
        return self.access_level in ["state", "county"] or self.is_admin()

    def has_precinct_access(self) -> bool:
        """Check if user has precinct access."""
        return self.access_level in ["state", "county", "precinct"] or self.is_admin()

    def to_dict(self) -> dict:
        """Convert user object to dictionary with camelCase keys for frontend."""
        return {
            "id": self.id,
            "firstName": self.first_name,
            "lastName": self.last_name,
            "email": self.email,
            "mobile": self.mobile,
            "userGroup": self.user_group,
            "loginEnabled": self.login_enabled,
            "loginId": self.login_id,
            "accessLevel": self.access_level,
            "county": self.county,
            "precinct": self.precinct,
            "status": self.status,
            "image": self.image,
            "company": self.company,
            "employeeNo": self.employee_no,
            "manager": self.manager,
            "department": self.department,
            "location": self.location,
            "addressLine1": self.address_line1,
            "addressLine2": self.address_line2,
            "city": self.city,
            "state": self.state,
            "pincode": self.pincode,
            "country": self.country,
            "role": self.role,
            "username": self.username,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
            "fullName": self.get_full_name()
        }