// src/services/damageReportService.ts
// Frontend API service for Damage Report Management

import {
  DamageReport,
  DamageReportCreate,
  DamageReportUpdate,
  DamageReportFilters,
  DamageType,
  DamageSeverity,
  DamageStatus,
  ApiResponse,
  PaginatedResponse
} from '../types/workflow';

// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class DamageReportService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/damage-reports${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || errorData?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Damage Report API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Get all damage reports with filters and pagination
  async getDamageReports(filters: DamageReportFilters = {}): Promise<PaginatedResponse<DamageReport>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const endpoint = params.toString() ? `?${params.toString()}` : '';
    return this.makeRequest<PaginatedResponse<DamageReport>>(endpoint);
  }

  // Get a specific damage report by ID
  async getDamageReport(id: number): Promise<ApiResponse<DamageReport>> {
    return this.makeRequest<ApiResponse<DamageReport>>(`/${id}`);
  }

  // Create a new damage report
  async createDamageReport(reportData: DamageReportCreate): Promise<ApiResponse<DamageReport>> {
    return this.makeRequest<ApiResponse<DamageReport>>('', {
      method: 'POST',
      body: JSON.stringify(reportData),
    });
  }

  // Update an existing damage report
  async updateDamageReport(id: number, updateData: DamageReportUpdate): Promise<ApiResponse<DamageReport>> {
    return this.makeRequest<ApiResponse<DamageReport>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  // Delete a damage report
  async deleteDamageReport(id: number): Promise<ApiResponse<null>> {
    return this.makeRequest<ApiResponse<null>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  // Get damage reports for a specific asset
  async getDamageReportsForAsset(assetId: number): Promise<ApiResponse<DamageReport[]>> {
    return this.makeRequest<ApiResponse<DamageReport[]>>(`/asset/${assetId}`);
  }

  // Update damage report status
  async updateDamageStatus(
    id: number, 
    status: DamageStatus, 
    resolution?: string,
    notes?: string
  ): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, { status, resolution, notes });
  }

  // Mark damage as assessed
  async markAssessed(
    id: number,
    estimatedRepairCost?: number,
    repairVendor?: string,
    notes?: string
  ): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, {
      status: DamageStatus.ASSESSED,
      estimatedRepairCost,
      repairVendor,
      notes,
    });
  }

  // Schedule repair
  async scheduleRepair(
    id: number,
    repairStartDate: string,
    repairVendor: string,
    estimatedRepairCost?: number
  ): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, {
      status: DamageStatus.REPAIR_SCHEDULED,
      repairStartDate,
      repairVendor,
      estimatedRepairCost,
    });
  }

  // Start repair
  async startRepair(id: number, notes?: string): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, {
      status: DamageStatus.UNDER_REPAIR,
      notes,
    });
  }

  // Complete repair
  async completeRepair(
    id: number,
    repairCompletionDate: string,
    actualRepairCost?: number,
    resolution?: string,
    preventiveMeasures?: string
  ): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, {
      status: DamageStatus.REPAIR_COMPLETED,
      repairCompletionDate,
      actualRepairCost,
      resolution,
      preventiveMeasures,
    });
  }

  // Mark as write-off
  async markWriteOff(
    id: number,
    resolution: string,
    notes?: string
  ): Promise<ApiResponse<DamageReport>> {
    return this.updateDamageReport(id, {
      status: DamageStatus.WRITE_OFF,
      resolution,
      notes,
    });
  }

  // Upload damage photos
  async uploadDamagePhotos(id: number, photos: File[]): Promise<ApiResponse<DamageReport>> {
    const formData = new FormData();
    photos.forEach((photo, index) => {
      formData.append(`photos`, photo);
    });

    return this.makeRequest<ApiResponse<DamageReport>>(`/${id}/photos`, {
      method: 'POST',
      body: formData,
      headers: {}, // Remove Content-Type to let browser set it for FormData
    });
  }

  // Delete damage photo
  async deleteDamagePhoto(id: number, photoId: string): Promise<ApiResponse<DamageReport>> {
    return this.makeRequest<ApiResponse<DamageReport>>(`/${id}/photos/${photoId}`, {
      method: 'DELETE',
    });
  }

  // Get damage statistics
  async getDamageStats(
    startDate?: string,
    endDate?: string,
    damageType?: DamageType,
    severity?: DamageSeverity
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (damageType) params.append('damage_type', damageType);
    if (severity) params.append('severity', severity);

    const endpoint = params.toString() ? `/stats?${params.toString()}` : '/stats';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }

  // Get pending damage reports
  async getPendingDamageReports(): Promise<ApiResponse<DamageReport[]>> {
    return this.getDamageReports({
      status: DamageStatus.REPORTED,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get high severity damage reports
  async getHighSeverityReports(): Promise<ApiResponse<DamageReport[]>> {
    return this.getDamageReports({
      severity: DamageSeverity.CRITICAL,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get damage reports by type
  async getDamageReportsByType(damageType: DamageType): Promise<ApiResponse<DamageReport[]>> {
    return this.getDamageReports({
      damageType,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Bulk update damage reports
  async bulkUpdateDamageReports(
    reportIds: number[],
    updateData: Partial<DamageReportUpdate>
  ): Promise<ApiResponse<DamageReport[]>> {
    return this.makeRequest<ApiResponse<DamageReport[]>>('/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({
        reportIds,
        updateData,
      }),
    });
  }

  // Get damage report history for reporting
  async getDamageHistory(
    startDate: string,
    endDate: string,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
      format,
    });

    return this.makeRequest<any>(`/history?${params.toString()}`);
  }

  // Generate damage report document
  async generateDamageReportDocument(id: number, template: string = 'default'): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>(`/${id}/document`, {
      method: 'POST',
      body: JSON.stringify({ template }),
    });
  }

  // Get repair cost summary
  async getRepairCostSummary(
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const endpoint = params.toString() ? `/repair-costs?${params.toString()}` : '/repair-costs';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }
}

export const damageReportService = new DamageReportService();
export default damageReportService;
