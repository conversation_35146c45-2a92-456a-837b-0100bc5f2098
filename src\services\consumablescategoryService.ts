// Frontend Consumable Category Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

const API_URL = '/api/consumables-category';

export interface ConsumableCategory {
  id: string;
  name: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ConsumableCategoryCreationData {
  name: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class ConsumableCategoryService {
  // Get all consumable categories
  async getAllConsumableCategories(): Promise<{ consumableCategories: ConsumableCategory[]; count: number }> {
    try {
      const response = await apiCall('/consumables-category');
      if (response.success) {
        return {
          consumableCategories: response.data.consumableCategories,
          count: response.data.count,
        };
      }
      throw new Error(response.message || 'Failed to fetch consumable categories');
    } catch (error) {
      console.error('Error fetching consumable categories:', error);
      throw error;
    }
  }

  // Get consumable category by ID
  async getConsumableCategoryById(id: string): Promise<ConsumableCategory> {
    try {
      const response = await apiCall(`/consumables-category/${id}`);
      if (response.success) {
        return response.data.consumableCategory;
      }
      throw new Error(response.message || 'Failed to fetch consumable category');
    } catch (error) {
      console.error('Error fetching consumable category by ID:', error);
      throw error;
    }
  }

  // Create new consumable category
  async createConsumableCategory(categoryData: ConsumableCategoryCreationData): Promise<ConsumableCategory> {
    try {
      const response = await apiCall('/consumables-category', {
        method: 'POST',
        body: JSON.stringify(categoryData),
      });
      if (response.success) {
        return response.data.consumableCategory;
      }
      throw new Error(response.message || 'Failed to create consumable category');
    } catch (error) {
      console.error('Error creating consumable category:', error);
      throw error;
    }
  }

  // Update consumable category
  async updateConsumableCategory(id: string, categoryData: Partial<ConsumableCategoryCreationData>): Promise<ConsumableCategory> {
    try {
      const response = await apiCall(`/consumables-category/${id}`, {
        method: 'PUT',
        body: JSON.stringify(categoryData),
      });
      if (response.success) {
        return response.data.consumableCategory;
      }
      throw new Error(response.message || 'Failed to update consumable category');
    } catch (error) {
      console.error('Error updating consumable category:', error);
      throw error;
    }
  }

  // Delete consumable category
  async deleteConsumableCategory(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/consumables-category/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting consumable category:', error);
      throw error;
    }
  }

  // Helper method to prepare consumable categories for export
  prepareConsumableCategoriesForExport(consumableCategories: ConsumableCategory[]) {
    return consumableCategories.map(category => ({
      ID: category.id,
      'Consumable Category': category.name,
      'Status': category.status ? 'Active' : 'Inactive',
    //   'Created At': new Date(category.createdAt).toLocaleDateString(),
    //   'Updated At': new Date(category.updatedAt).toLocaleDateString()
    }));
  }
}

const consumableCategoryService = new ConsumableCategoryService();
export default consumableCategoryService;
