# app/models/supply_checklist.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.config.database import Base
import enum

class SupplyChecklistStatus(str, enum.Enum):
    PENDING = "Pending"
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    CANCELLED = "Cancelled"

class SupplyChecklistType(str, enum.Enum):
    PACK = "Pack"
    UNPACK = "Unpack"

class SupplyChecklist(Base):
    __tablename__ = "supply_checklists"

    id = Column(Integer, primary_key=True, autoincrement=True)
    checklist_id = Column(String(100), nullable=False, unique=True, index=True)
    checklist_type = Column(Enum(SupplyChecklistType), nullable=False)
    status = Column(Enum(SupplyChecklistStatus), nullable=False, default=SupplyChecklistStatus.PENDING)
    created_by = Column(String(36), Foreign<PERSON>ey("users.id"), nullable=False)
    
    # Packing/Unpacking details
    packing_list_id = Column(Integer, ForeignKey("packing_page.id"), nullable=True)
    rolling_cage_id = Column(String(36), nullable=True)  # Remove FK constraint for now
    
    # Session details
    election_id = Column(String(100), nullable=True)
    location = Column(String(255), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    created_by_user = relationship("User", foreign_keys=[created_by])
    supply_items = relationship("SupplyChecklistItem", back_populates="checklist", cascade="all, delete-orphan")

    def to_dict(self) -> dict:
        """Convert supply checklist object to dictionary."""
        return {
            "id": self.id,
            "checklist_id": self.checklist_id,
            "checklist_type": self.checklist_type,
            "status": self.status,
            "created_by": self.created_by,
            "packing_list_id": self.packing_list_id,
            "rolling_cage_id": self.rolling_cage_id,
            "election_id": self.election_id,
            "location": self.location,
            "notes": self.notes,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

class SupplyChecklistItem(Base):
    __tablename__ = "supply_checklist_items"

    id = Column(Integer, primary_key=True, autoincrement=True)
    checklist_id = Column(Integer, ForeignKey("supply_checklists.id"), nullable=False)
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=False)
    previous_status = Column(String(50), nullable=True)
    new_status = Column(String(50), nullable=False)
    processed_by = Column(String(36), ForeignKey("users.id"), nullable=True)
    notes = Column(Text, nullable=True)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    checklist = relationship("SupplyChecklist", back_populates="supply_items")
    asset = relationship("Asset", foreign_keys=[asset_id])
    processed_by_user = relationship("User", foreign_keys=[processed_by])

    def to_dict(self) -> dict:
        """Convert supply checklist item object to dictionary."""
        return {
            "id": self.id,
            "checklist_id": self.checklist_id,
            "asset_id": self.asset_id,
            "previous_status": self.previous_status,
            "new_status": self.new_status,
            "processed_by": self.processed_by,
            "notes": self.notes,
            "processed_at": self.processed_at,
            "created_at": self.created_at
        }
