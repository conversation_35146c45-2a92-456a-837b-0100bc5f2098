#!/usr/bin/env python3
"""
Test all key endpoints to verify frontend-backend integration.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8000/api"

def test_endpoints():
    """Test all key endpoints that the frontend uses."""
    
    print("🚀 Testing Frontend-Backend Integration Endpoints")
    print("=" * 60)
    
    # Test endpoints that should work without authentication
    public_endpoints = [
        "/health",
        "/"
    ]
    
    # Test endpoints that require authentication (should return 403)
    protected_endpoints = [
        "/api/assets",
        "/api/la-checklist/available-assets",
        "/api/la-checklist/sessions", 
        "/api/supply-checklist/checklists",
        "/api/packing-lists",
        "/api/transaction-orders",
        "/api/asset-status-history/recent",
        "/api/transaction-orders/recent",
        "/api/asset-types",
        "/api/asset-models",
        "/api/asset-status",
        "/api/masters/locations",
        "/api/masters/consumables"
    ]
    
    success_count = 0
    total_tests = 0
    
    print("\n📋 Testing Public Endpoints (should return 200)")
    print("-" * 40)
    
    for endpoint in public_endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - Status: {response.status_code}")
                success_count += 1
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                
            total_tests += 1
            
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
            total_tests += 1
    
    print("\n🔒 Testing Protected Endpoints (should return 403 - Not Authenticated)")
    print("-" * 40)
    
    for endpoint in protected_endpoints:
        try:
            url = f"http://localhost:8000{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 403:
                print(f"✅ {endpoint} - Status: {response.status_code} (Correctly Protected)")
                success_count += 1
            elif response.status_code == 404:
                print(f"⚠️  {endpoint} - Status: {response.status_code} (Endpoint Not Found)")
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                
            total_tests += 1
            
        except Exception as e:
            print(f"❌ {endpoint} - Error: {e}")
            total_tests += 1
    
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    print(f"✅ Successful: {success_count}/{total_tests}")
    print(f"❌ Failed: {total_tests - success_count}/{total_tests}")
    print(f"📈 Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count >= total_tests * 0.8:  # 80% success rate
        print("\n🎉 Backend is ready for frontend integration!")
        print("✅ All key endpoints are responding correctly")
        print("🔧 Your frontend can now connect to these APIs")
        
        print("\n📋 Next Steps:")
        print("1. Start your frontend development server")
        print("2. Test the login functionality")
        print("3. Verify that all pages load correctly")
        print("4. Add your asset types, models, and statuses")
        print("5. Start using the complete workflow!")
        
        return True
    else:
        print(f"\n⚠️  Some endpoints need attention")
        print("🔧 Check the failed endpoints above")
        return False

if __name__ == "__main__":
    print("🔧 Starting Backend Integration Tests...")
    success = test_endpoints()
    
    if success:
        print("\n✅ Backend is ready! Your frontend integration should work.")
    else:
        print("\n❌ Some issues found. Check the output above.")
        sys.exit(1)
