# Hierarchical Role-Based Access Control (RBAC) Implementation Guide

## Overview

This document describes the comprehensive hierarchical Role-Based Access Control (RBAC) system implemented for the asset management application. The system provides strict data segregation based on geographical hierarchy with three user levels: state, county, and precinct.

## Architecture

### User Hierarchy & Access Levels

1. **State-level users**: Can view and manage all assets across all counties and precincts within their assigned state
2. **County-level users**: Can only access assets within their specific county and its precincts  
3. **Precinct-level users**: Can only view and manage assets within their specific precinct

### Database Schema Enhancements

#### User Model Updates (`app/models/user.py`)
```python
# Enhanced geographical hierarchy fields
access_level = Column(String(20), nullable=False, index=True)  # state/county/precinct
state = Column(String(100), nullable=True, index=True)
county = Column(String(100), nullable=True, index=True)  
precinct = Column(String(100), nullable=True, index=True)
```

#### Asset Model Updates (`app/models/assets.py`)
```python
# Required geographical hierarchy for RBAC
state = Column(String(100), nullable=False, index=True)
county = Column(String(100), nullable=False, index=True)
precinct = Column(String(100), nullable=True, index=True)
```

#### Key Methods Added to User Model
- `validate_geographical_scope()`: Validates user has proper geographical scope
- `can_access_asset()`: Checks if user can access specific asset
- `get_asset_query_filters()`: Returns query filters based on user scope

## Backend Implementation

### RBAC Middleware (`app/middleware/rbac.py`)

#### RBACService Class
The core service class that handles all RBAC operations:

```python
class RBACService:
    def __init__(self, db: Session, current_user: User)
    
    # Core methods:
    def get_user_scope() -> Dict[str, Any]
    def apply_asset_filters(query: Query) -> Query
    def apply_transfer_filters(query: Query) -> Query
    def can_access_asset(asset: Asset) -> bool
    def can_create_asset(asset_data: Dict) -> bool
    def can_initiate_transfer(from_location: Dict, to_location: Dict) -> bool
```

#### Dependency Functions
- `get_rbac_service()`: FastAPI dependency to inject RBAC service
- `require_asset_access()`: Validates asset access permissions
- `validate_location_access()`: Validates location access level

### API Endpoint Security

#### Assets Routes (`app/routes/assets.py`)
- **GET /assets**: Automatically filters assets based on user's geographical scope
- **GET /assets/{id}**: Validates user can access specific asset
- **POST /assets**: Validates user can create asset in specified location
- **PUT /assets/{id}**: Validates user can modify asset
- **DELETE /assets/{id}**: Admin-only operation

#### Asset Transfers Routes (`app/routes/asset_transfers.py`)
- **GET /asset-transfers**: Filters transfers based on user's geographical involvement
- **POST /asset-transfers**: Validates user can initiate transfer from source location
- **PUT /asset-transfers/{id}**: Validates user involvement in transfer
- **POST /asset-transfers/{id}/verify**: Validates user can verify transfer

### Query Filtering Logic

#### State-level Users
```python
# Can see all assets in their state
filters = {"state": user.state}
```

#### County-level Users  
```python
# Can see assets in their county only
filters = {
    "state": user.state,
    "county": user.county
}
```

#### Precinct-level Users
```python
# Can see assets in their precinct only
filters = {
    "state": user.state,
    "county": user.county, 
    "precinct": user.precinct
}
```

## Frontend Implementation

### Role-Based UI (`src/pages/transactions/Transactionstabs/TransferTracking.tsx`)

#### User Scope Loading
```typescript
useEffect(() => {
  const scope = authService.getUserScope();
  setUserScope(scope);
  // Set allowed locations based on access level
}, []);
```

#### Conditional Location Dropdowns
- **State users**: See all locations in their state
- **County users**: See locations in their county only  
- **Precinct users**: See their precinct and county warehouses only

#### AuthService Updates (`src/services/authService.ts`)
```typescript
getUserScope(): {
  accessLevel: string;
  state?: string;
  county?: string; 
  precinct?: string;
  canViewAll: boolean;
}
```

## User Management Integration

### Enhanced User Creation (`src/components/users/UserAddEditDialog.tsx`)

#### Geographical Scope Validation
```typescript
const validateGeographicalScope = (data: UserCreationData): string[] => {
  const errors: string[] = [];
  
  if (data.accessLevel === 'state' && !data.state) {
    errors.push('State is required for state-level access');
  }
  // Additional validations for county and precinct levels
  
  return errors;
};
```

#### Conditional Form Fields
- **State field**: Required for all non-admin users
- **County field**: Required for county and precinct users
- **Precinct field**: Required for precinct users only

### Backend User Validation (`app/routes/auth.py`)
```python
# RBAC validation during user creation
if access_level == "state" and not state:
    return {"success": False, "message": "State is required for state-level access"}
elif access_level == "county" and (not state or not county):
    return {"success": False, "message": "State and county are required for county-level access"}
elif access_level == "precinct" and (not state or not county or not precinct):
    return {"success": False, "message": "State, county, and precinct are required for precinct-level access"}
```

## Database Migration

### Migration Script (`migrations/add_rbac_enhancements.py`)
1. **Adds database indexes** for performance on RBAC fields
2. **Updates existing assets** with default geographical values
3. **Updates existing users** with proper geographical scope
4. **Validates data integrity** after migration

### Running the Migration
```bash
cd asset_management_api
python migrations/add_rbac_enhancements.py
```

## Testing & Validation

### Comprehensive Test Suite (`tests/test_rbac.py`)
- **User scope validation tests**
- **Asset access control tests** 
- **Transfer permission tests**
- **Data segregation validation**

### Manual Validation Script (`test_rbac_implementation.py`)
```bash
cd asset_management_api
python test_rbac_implementation.py
```

## Security Features

### Data Segregation
- **Automatic query filtering**: All asset queries automatically filtered by user scope
- **Transfer validation**: Users can only initiate transfers from locations they control
- **Asset creation restrictions**: Users can only create assets in their geographical scope

### Permission Validation
- **Individual asset access**: Each asset access validated against user scope
- **Bulk operation filtering**: List operations automatically filtered
- **Cross-boundary prevention**: Prevents access to data outside user's scope

### Audit Trail
- **User scope logging**: All RBAC decisions logged
- **Access attempt tracking**: Failed access attempts recorded
- **Geographical scope validation**: User scope validated on each request

## Usage Examples

### Creating a County-level User
```json
{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "accessLevel": "county",
  "state": "South Carolina",
  "county": "Charleston",
  "role": "manager"
}
```

### Asset Query for County User
```python
# Automatically filtered to Charleston county only
assets = rbac.apply_asset_filters(db.query(Asset)).all()
```

### Transfer Permission Check
```python
from_location = {"state": "SC", "county": "Charleston", "precinct": "P1"}
to_location = {"state": "SC", "county": "Greenville", "precinct": "P1"}

# County user in Charleston can initiate this transfer
can_transfer = rbac.can_initiate_transfer(from_location, to_location)
```

## Best Practices

1. **Always use RBAC service**: Never bypass RBAC filtering in queries
2. **Validate on both frontend and backend**: Double validation for security
3. **Log access attempts**: Maintain audit trail for compliance
4. **Regular scope validation**: Periodically validate user geographical scope
5. **Test thoroughly**: Use provided test suite to validate changes

## Troubleshooting

### Common Issues
1. **User cannot see assets**: Check geographical scope validation
2. **Transfer permission denied**: Verify user has access to source location
3. **Asset creation fails**: Ensure asset location matches user scope

### Debug Commands
```python
# Check user scope
user_scope = rbac.get_user_scope()

# Test asset access
can_access = rbac.can_access_asset(asset)

# Validate geographical scope
is_valid = user.validate_geographical_scope()
```

## Future Enhancements

1. **Dynamic location management**: API-driven location hierarchy
2. **Role inheritance**: Automatic permission inheritance
3. **Temporary access grants**: Time-limited cross-boundary access
4. **Advanced audit reporting**: Detailed access pattern analysis
