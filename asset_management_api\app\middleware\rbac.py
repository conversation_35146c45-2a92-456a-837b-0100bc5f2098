# app/middleware/rbac.py
"""
Role-Based Access Control (RBAC) middleware for hierarchical data filtering.
Provides comprehensive access control based on geographical hierarchy.
"""

from fastapi import HTTPException, status, Depends
from sqlalchemy.orm import Session, Query
from sqlalchemy import and_, or_
from typing import Dict, Any, List, Optional, Type, TypeVar
from app.config.database import get_db
from app.models.user import User
from app.models.assets import Asset
from app.models.asset_transfers import AssetTransfer
from app.middleware.auth import get_current_user
import logging

logger = logging.getLogger(__name__)

# Generic type for SQLAlchemy models
ModelType = TypeVar('ModelType')

class RBACService:
    """Service class for Role-Based Access Control operations."""
    
    def __init__(self, db: Session, current_user: User):
        self.db = db
        self.current_user = current_user
        
    def get_user_scope(self) -> Dict[str, Any]:
        """Get the user's geographical scope for data filtering."""
        if self.current_user.is_admin():
            return {
                "is_admin": True,
                "access_level": "admin",
                "can_view_all": True,
                "filters": {}
            }
        
        scope = {
            "is_admin": False,
            "access_level": self.current_user.access_level,
            "state": self.current_user.state,
            "county": self.current_user.county,
            "precinct": self.current_user.precinct,
            "can_view_all": False
        }
        
        # Build filters based on access level
        filters = {}
        if self.current_user.access_level == "state":
            filters["state"] = self.current_user.state
        elif self.current_user.access_level == "county":
            filters["state"] = self.current_user.state
            filters["county"] = self.current_user.county
        elif self.current_user.access_level == "precinct":
            filters["state"] = self.current_user.state
            filters["county"] = self.current_user.county
            filters["precinct"] = self.current_user.precinct
            
        scope["filters"] = filters
        return scope
    
    def apply_asset_filters(self, query: Query) -> Query:
        """Apply RBAC filters to asset queries."""
        if self.current_user.is_admin():
            return query
            
        scope = self.get_user_scope()
        filters = scope["filters"]
        
        conditions = []
        for field, value in filters.items():
            if hasattr(Asset, field) and value is not None:
                conditions.append(getattr(Asset, field) == value)
        
        if conditions:
            query = query.filter(and_(*conditions))
            
        return query
    
    def apply_transfer_filters(self, query: Query) -> Query:
        """Apply RBAC filters to asset transfer queries."""
        if self.current_user.is_admin():
            return query
            
        scope = self.get_user_scope()
        
        # For transfers, user can see transfers that involve their geographical scope
        # either as source or destination
        if self.current_user.access_level == "state":
            # State users can see all transfers within their state
            state_condition = or_(
                AssetTransfer.from_county.like(f"%{self.current_user.state}%"),
                AssetTransfer.to_county.like(f"%{self.current_user.state}%")
            )
            query = query.filter(state_condition)
            
        elif self.current_user.access_level == "county":
            # County users can see transfers involving their county
            county_condition = or_(
                AssetTransfer.from_county == self.current_user.county,
                AssetTransfer.to_county == self.current_user.county
            )
            query = query.filter(county_condition)
            
        elif self.current_user.access_level == "precinct":
            # Precinct users can see transfers involving their precinct or county
            precinct_condition = or_(
                AssetTransfer.from_precinct == self.current_user.precinct,
                AssetTransfer.to_precinct == self.current_user.precinct,
                AssetTransfer.from_county == self.current_user.county,
                AssetTransfer.to_county == self.current_user.county
            )
            query = query.filter(precinct_condition)
            
        return query
    
    def can_access_asset(self, asset: Asset) -> bool:
        """Check if current user can access a specific asset."""
        if self.current_user.is_admin():
            return True
            
        return self.current_user.can_access_asset(
            asset.state, asset.county, asset.precinct
        )
    
    def can_create_asset(self, asset_data: Dict[str, Any]) -> bool:
        """Check if current user can create an asset in the specified location."""
        if self.current_user.is_admin():
            return True
            
        return self.current_user.can_access_asset(
            asset_data.get("state"),
            asset_data.get("county"),
            asset_data.get("precinct")
        )
    
    def can_initiate_transfer(self, from_location: Dict[str, str], to_location: Dict[str, str]) -> bool:
        """Check if current user can initiate a transfer between locations."""
        if self.current_user.is_admin():
            return True
            
        # User must have access to the source location to initiate transfer
        can_access_from = self.current_user.can_access_asset(
            from_location.get("state"),
            from_location.get("county"),
            from_location.get("precinct")
        )
        
        return can_access_from
    
    def get_allowed_locations(self) -> Dict[str, List[str]]:
        """Get locations that the user is allowed to access."""
        if self.current_user.is_admin():
            return {"all": True}
            
        allowed = {
            "states": [self.current_user.state] if self.current_user.state else [],
            "counties": [self.current_user.county] if self.current_user.county else [],
            "precincts": [self.current_user.precinct] if self.current_user.precinct else []
        }
        
        return allowed
    
    def validate_geographical_data(self, data: Dict[str, Any], operation: str = "create") -> bool:
        """Validate geographical data against user's scope."""
        if self.current_user.is_admin():
            return True
            
        state = data.get("state")
        county = data.get("county")
        precinct = data.get("precinct")
        
        # Check if user can access the specified location
        return self.current_user.can_access_asset(state, county, precinct)

# Dependency functions for FastAPI routes
def get_rbac_service(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> RBACService:
    """Dependency to get RBAC service instance."""
    return RBACService(db, current_user)

def require_asset_access(asset_id: int):
    """Dependency factory to check asset access permissions."""
    def asset_access_checker(
        rbac: RBACService = Depends(get_rbac_service)
    ):
        asset = rbac.db.query(Asset).filter(Asset.id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        if not rbac.can_access_asset(asset):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access this asset"
            )
        
        return asset
    
    return asset_access_checker

def require_transfer_permissions(from_location: Dict[str, str], to_location: Dict[str, str]):
    """Dependency factory to check transfer permissions."""
    def transfer_permission_checker(
        rbac: RBACService = Depends(get_rbac_service)
    ):
        if not rbac.can_initiate_transfer(from_location, to_location):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to initiate transfer between these locations"
            )
        return True
    
    return transfer_permission_checker

def validate_location_access(required_access_level: str = "county"):
    """Dependency factory to validate user's location access level."""
    def location_access_checker(
        rbac: RBACService = Depends(get_rbac_service)
    ):
        user_scope = rbac.get_user_scope()
        
        if user_scope["is_admin"]:
            return True
        
        access_levels = {
            "precinct": 1,
            "county": 2,
            "state": 3
        }
        
        user_level = access_levels.get(user_scope["access_level"], 0)
        required_level = access_levels.get(required_access_level, 0)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Requires {required_access_level} level access or higher"
            )
        
        return True
    
    return location_access_checker

def require_admin(
    current_user: User = Depends(get_current_user)
):
    """Dependency to check if current user is an admin."""
    if not current_user.is_admin():
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

# Advanced RBAC utility functions
def apply_rbac_filters(query: Query, model_class: Type[ModelType], current_user: User) -> Query:
    """
    Apply RBAC filters to any query based on the model class and user permissions.
    This is a generic function that can be used across different models.
    """
    if current_user.is_admin():
        return query
    
    # Apply filters based on model type
    if model_class == Asset:
        # Apply asset-specific filters
        conditions = []
        if current_user.state:
            conditions.append(Asset.state == current_user.state)
        if current_user.county and current_user.access_level in ["county", "precinct"]:
            conditions.append(Asset.county == current_user.county)
        if current_user.precinct and current_user.access_level == "precinct":
            conditions.append(Asset.precinct == current_user.precinct)
        
        if conditions:
            query = query.filter(and_(*conditions))
    
    # Add more model-specific filters as needed
    return query

def enforce_rbac(model_class: Type[ModelType] = None, operation: str = "read"):
    """
    Decorator to enforce RBAC on route functions.
    Can be used to automatically apply filters based on user permissions.
    """
    from functools import wraps
    from typing import Callable
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract RBAC service from kwargs if available
            rbac_service = kwargs.get('rbac')
            if rbac_service and model_class:
                # Apply model-specific RBAC logic
                if operation == "read" and model_class == Asset:
                    # Example: modify query to include RBAC filters
                    pass
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
