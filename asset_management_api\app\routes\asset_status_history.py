# app/routes/asset_status_history.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.models.asset_status_history import AssetStatusHistory
from app.models.assets import Asset, AssetStatus
from app.models.user import User
from app.middleware.auth import get_current_user
from app.schemas.asset_status_history import (
    AssetStatusHistoryCreate, AssetStatusHistoryResponse, AssetStatusChangeRequest
)
from typing import List, Optional
import logging
from datetime import datetime
from sqlalchemy import desc

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/asset/{asset_id}", response_model=List[AssetStatusHistoryResponse])
async def get_asset_status_history(
    asset_id: int,
    limit: int = Query(50, ge=1, le=200),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get status history for a specific asset."""
    try:
        # Verify asset exists
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        history = db.query(AssetStatusHistory).filter(
            AssetStatusHistory.asset_id == asset_id
        ).order_by(AssetStatusHistory.created_at.desc()).limit(limit).all()
        
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching asset status history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch asset status history"
        )

@router.get("/", response_model=List[AssetStatusHistoryResponse])
async def get_all_status_history(
    limit: int = Query(100, ge=1, le=500),
    workflow_module: Optional[str] = Query(None),
    new_status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get status history with optional filtering."""
    try:
        query = db.query(AssetStatusHistory)
        
        if workflow_module:
            query = query.filter(AssetStatusHistory.workflow_module == workflow_module)
        
        if new_status:
            query = query.filter(AssetStatusHistory.new_status == new_status)
        
        history = query.order_by(AssetStatusHistory.created_at.desc()).limit(limit).all()
        
        return history
        
    except Exception as e:
        logger.error(f"Error fetching status history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch status history"
        )

@router.post("/change-status", response_model=AssetStatusHistoryResponse)
async def change_asset_status(
    status_change: AssetStatusChangeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Manually change an asset's status and log the change."""
    try:
        # Verify asset exists
        asset = db.query(Asset).filter(Asset.id == status_change.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Validate new status
        try:
            new_status_enum = AssetStatus(status_change.new_status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status: {status_change.new_status}"
            )
        
        previous_status = asset.status
        
        # Create status history record
        status_history = AssetStatusHistory(
            asset_id=status_change.asset_id,
            previous_status=previous_status,
            new_status=status_change.new_status,
            changed_by=current_user.id,
            change_reason=status_change.change_reason,
            workflow_module=status_change.workflow_module or "MANUAL",
            session_id=status_change.session_id,
            notes=status_change.notes
        )
        
        # Update asset status
        asset.status = new_status_enum
        asset.updated_at = datetime.utcnow()
        
        db.add(status_history)
        db.commit()
        db.refresh(status_history)
        
        return status_history
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error changing asset status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change asset status"
        )

@router.get("/workflow-modules", response_model=List[str])
async def get_workflow_modules(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of distinct workflow modules from status history."""
    try:
        modules = db.query(AssetStatusHistory.workflow_module).distinct().filter(
            AssetStatusHistory.workflow_module.isnot(None)
        ).all()
        
        return [module[0] for module in modules if module[0]]
        
    except Exception as e:
        logger.error(f"Error fetching workflow modules: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch workflow modules"
        )

@router.get("/status-summary", response_model=dict)
async def get_status_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get summary of asset statuses across the system."""
    try:
        # Get current status counts
        status_counts = {}
        for status in AssetStatus:
            count = db.query(Asset).filter(Asset.status == status).count()
            status_counts[status.value] = count
        
        # Get recent status changes (last 7 days)
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        
        recent_changes = db.query(AssetStatusHistory).filter(
            AssetStatusHistory.created_at >= week_ago
        ).count()
        
        # Get workflow activity
        workflow_activity = db.query(
            AssetStatusHistory.workflow_module,
            db.func.count(AssetStatusHistory.id).label('count')
        ).filter(
            AssetStatusHistory.created_at >= week_ago,
            AssetStatusHistory.workflow_module.isnot(None)
        ).group_by(AssetStatusHistory.workflow_module).all()
        
        workflow_counts = {activity.workflow_module: activity.count for activity in workflow_activity}
        
        return {
            "status_counts": status_counts,
            "recent_changes": recent_changes,
            "workflow_activity": workflow_counts,
            "total_assets": sum(status_counts.values())
        }
        
    except Exception as e:
        logger.error(f"Error getting status summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get status summary"
        )

@router.get("/recent", response_model=List[dict])
async def get_recent_asset_transactions(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recent asset status changes for transaction display."""
    try:
        recent_history = db.query(AssetStatusHistory).join(Asset).order_by(
            AssetStatusHistory.created_at.desc()
        ).limit(limit).all()

        transactions = []
        for history in recent_history:
            asset = history.asset
            transaction_type = "Check Out" if history.new_status in ["CHECKED_OUT", "IN_USE"] else "Check In"

            transactions.append({
                "id": history.id,
                "asset": asset.asset_id if asset else "Unknown",
                "asset_id": asset.asset_id if asset else "Unknown",
                "type": transaction_type,
                "user": history.changed_by,
                "created_by": history.changed_by,
                "date": history.created_at.isoformat() if history.created_at else None,
                "status": history.new_status,
                "notes": history.notes
            })

        return transactions

    except Exception as e:
        logger.error(f"Error getting recent asset transactions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get recent asset transactions"
        )
