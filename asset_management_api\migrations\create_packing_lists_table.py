"""Create packing_lists table migration"""

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_packing_lists_table():
    """Create the packing_lists table."""
    engine = create_engine(DATABASE_URL)
    
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS packing_lists (
        id VARCHAR(255) PRIMARY KEY,
        election VARCHAR(255) NOT NULL,
        to VARCHAR(255) NOT NULL,
        from_location VARCHAR(255) NOT NULL,
        packing_date DATETIME,
        packing_user VARCHAR(255),
        supply_package VARCHAR(255) NOT NULL,
        item VARCHAR(255) NOT NULL,
        qty INTEGER NOT NULL DEFAULT 1,
        proofed_by VA<PERSON>HAR(255),
        verification_status VARCHAR(50) NOT NULL DEFAULT 'pending',
        delivery_date DATETIME,
        delivery_method VARCHAR(50),
        contact_person VARCHAR(255),
        unpacked_by <PERSON>RC<PERSON><PERSON>(255),
        status VARCHAR(50) NOT NULL DEFAULT 'packed',
        verification_items JSON,
        completion_status JSON,
        unpack_items JSON,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    """
    
    try:
        with engine.connect() as connection:
            connection.execute(text(create_table_sql))
            connection.commit()
            logger.info("✅ packing_lists table created successfully")
    except Exception as e:
        logger.error(f"❌ Error creating packing_lists table: {e}")
        raise e
    finally:
        engine.dispose()

if __name__ == "__main__":
    create_packing_lists_table()
    print("Migration completed successfully!") 