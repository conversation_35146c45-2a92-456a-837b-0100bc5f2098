// Frontend Other Cost Type Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface OtherCostType {
  cost: any;
  id: string;
  name: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface OtherCostTypeCreationData {
  name: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class OtherCostTypeService {
  // Get all other cost types
  async getAllOtherCostTypes(): Promise<{ otherCostTypes: OtherCostType[]; count: number }> {
    try {
      const response = await apiCall('/other-cost-types');
      if (response.success) {
        return {
          otherCostTypes: response.data.otherCostTypes,
          count: response.data.count
        };
      }
      throw new Error(response.message || 'Failed to fetch other cost types');
    } catch (error) {
      console.error('Error fetching other cost types:', error);
      throw error;
    }
  }

  // Get other cost type by ID
  async getOtherCostTypeById(id: string): Promise<OtherCostType> {
    try {
      const response = await apiCall(`/other-cost-types/${id}`);
      if (response.success) {
        return response.data.otherCostType;
      }
      throw new Error(response.message || 'Failed to fetch other cost type');
    } catch (error) {
      console.error('Error fetching other cost type by ID:', error);
      throw error;
    }
  }

  // Create new other cost type
  async createOtherCostType(otherCostTypeData: OtherCostTypeCreationData): Promise<OtherCostType> {
    try {
      const response = await apiCall('/other-cost-types', {
        method: 'POST',
        body: JSON.stringify(otherCostTypeData),
      });
      if (response.success) {
        return response.data.otherCostType;
      }
      throw new Error(response.message || 'Failed to create other cost type');
    } catch (error) {
      console.error('Error creating other cost type:', error);
      throw error;
    }
  }

  // Update other cost type
  async updateOtherCostType(id: string, otherCostTypeData: Partial<OtherCostTypeCreationData>): Promise<OtherCostType> {
    try {
      const response = await apiCall(`/other-cost-types/${id}`, {
        method: 'PUT',
        body: JSON.stringify(otherCostTypeData),
      });
      if (response.success) {
        return response.data.otherCostType;
      }
      throw new Error(response.message || 'Failed to update other cost type');
    } catch (error) {
      console.error('Error updating other cost type:', error);
      throw error;
    }
  }

  // Delete other cost type
  async deleteOtherCostType(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/other-cost-types/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting other cost type:', error);
      throw error;
    }
  }

  // Helper method to prepare other cost types for export
  prepareOtherCostTypesForExport(otherCostTypes: OtherCostType[]) {
    return otherCostTypes.map(otherCostType => ({
      ID: otherCostType.id,
      'Other Cost Type': otherCostType.name,
      'Status': otherCostType.status ? 'Active' : 'Inactive',
      'Created At': new Date(otherCostType.createdAt).toLocaleDateString(),
      'Updated At': new Date(otherCostType.updatedAt).toLocaleDateString()
    }));
  }
}

const otherCostTypeService = new OtherCostTypeService();
export default otherCostTypeService;