import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileDown, Edit, Eye, Trash, AlertTriangle } from "lucide-react";
import cageManagementService, { CageManagement, CageManagementCreationData } from "@/services/cagemanagementService";
import { RollingCageAddEditDialog } from "@/components/rolling-cage/RollingCageAddEditDialog";
import { RollingCageViewDialog } from "@/components/rolling-cage/RollingCageViewDialog";
import { exportData } from "@/utils/exportUtils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

export default function RollingCagePage() {
  const [rollingCages, setRollingCages] = useState<CageManagement[]>([]);
  const [filteredRollingCages, setFilteredRollingCages] = useState<CageManagement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRollingCage, setSelectedRollingCage] = useState<CageManagement | undefined>();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load rolling cages from backend
  useEffect(() => {
    loadRollingCages();
  }, []);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredRollingCages(rollingCages || []);
    } else {
      const lowercasedQuery = searchQuery.toLowerCase();
      const filtered = (rollingCages || []).filter(
        (cage) =>
          cage.refNo?.toLowerCase().includes(lowercasedQuery) ||
          cage.description?.toLowerCase().includes(lowercasedQuery)
      );
      setFilteredRollingCages(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, rollingCages]);

  const loadRollingCages = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await cageManagementService.getAllCageManagements();
      const cagesData = result?.cageManagements || [];
      setRollingCages(cagesData);
      setFilteredRollingCages(cagesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load rolling cages');
      console.error('Error loading rolling cages:', err);
      // Set empty arrays on error to prevent undefined issues
      setRollingCages([]);
      setFilteredRollingCages([]);
    } finally {
      setLoading(false);
    }
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = (filteredRollingCages || []).slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil((filteredRollingCages || []).length / itemsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Handle add rolling cage
  const handleAddRollingCage = async (cageData: CageManagementCreationData) => {
    try {
      const newCage = await cageManagementService.createCageManagement(cageData);
      setRollingCages([...(rollingCages || []), newCage]);
      setShowAddDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add rolling cage');
      console.error('Error adding rolling cage:', err);
    }
  };

  // Handle edit rolling cage
  const handleEditRollingCage = async (cageData: CageManagementCreationData) => {
    if (selectedRollingCage) {
      try {
        const updatedCage = await cageManagementService.updateCageManagement(
          selectedRollingCage.id, 
          cageData
        );

        setRollingCages((rollingCages || []).map(cage =>
          cage.id === updatedCage.id ? updatedCage : cage
        ));
        setShowEditDialog(false);
        setSelectedRollingCage(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update rolling cage');
        console.error('Error updating rolling cage:', err);
      }
    }
  };

  // Handle delete rolling cage
  const handleDeleteRollingCage = async () => {
    if (selectedRollingCage) {
      try {
        const success = await cageManagementService.deleteCageManagement(selectedRollingCage.id);
        if (success) {
          setRollingCages((rollingCages || []).filter(
            (cage) => cage.id !== selectedRollingCage.id
          ));
        }
        setShowDeleteDialog(false);
        setSelectedRollingCage(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete rolling cage');
        console.error('Error deleting rolling cage:', err);
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = cageManagementService.prepareCageManagementForExport(filteredRollingCages || []);

    exportData(
      exportableData,
      format,
      'Rolling_Cages_Export',
      'Rolling Cages List',
      ['Ref No', 'Description', 'Status', 'Last Updated By', 'Last Updated Date', 'Items Count']
    );
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading rolling cages...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600">{error}</p>
              <Button onClick={loadRollingCages} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Rolling Cage Management</h1>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
              <FileDown className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button size="sm" onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Rolling Cage
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Rolling Cages</CardTitle>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Last Updated By</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.length > 0 ? (
                  currentItems.map((cage) => (
                    <TableRow key={cage.id}>
                      <TableCell>{cage.refNo || 'N/A'}</TableCell>
                      <TableCell>{cage.description || 'N/A'}</TableCell>
                      <TableCell>{cage.lastUpdatedBy || 'N/A'}</TableCell>
                      <TableCell className="text-center">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          cage.status
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {cage.status ? 'Active' : 'Inactive'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRollingCage(cage);
                              setShowViewDialog(true);
                            }}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRollingCage(cage);
                              setShowEditDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedRollingCage(cage);
                              setShowDeleteDialog(true);
                            }}
                          >
                            <Trash className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                      No rolling cages found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>

            {/* Pagination */}
            {(filteredRollingCages || []).length > itemsPerPage && (
              <div className="flex justify-between items-center mt-4">
                <div>
                  Show <span className="font-medium">{itemsPerPage}</span> entries
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <Button variant="outline" size="sm" className="px-4">
                    {currentPage}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add Dialog */}
        <RollingCageAddEditDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          onSave={handleAddRollingCage}
        />

        {/* Edit Dialog */}
        <RollingCageAddEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          rollingCage={selectedRollingCage}
          onSave={handleEditRollingCage}
        />

        {/* View Dialog */}
        <RollingCageViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          rollingCage={selectedRollingCage}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the rolling cage
                "{selectedRollingCage?.refNo}" and remove all its data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteRollingCage}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </AppLayout>
  );
}
