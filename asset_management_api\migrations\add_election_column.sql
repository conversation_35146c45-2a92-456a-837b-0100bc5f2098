-- Add missing election column to packing_lists table
-- This fixes the error: Unknown column 'packing_lists.election' in 'field list'

-- Check if column exists and add it if it doesn't
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'packing_lists' 
    AND COLUMN_NAME = 'election'
);

-- Add the column if it doesn't exist
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE packing_lists ADD COLUMN election VARCHAR(255) NOT NULL DEFAULT "General Election 2024"',
    'SELECT "Column already exists" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the column was added
SELECT 'Election column migration completed' as status; 