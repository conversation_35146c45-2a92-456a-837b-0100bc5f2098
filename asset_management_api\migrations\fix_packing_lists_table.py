"""Fix packing_lists table by adding missing election column"""

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_packing_lists_table():
    """Add missing election column to packing_lists table."""
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # Check if election column exists
            check_column_sql = """
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'packing_lists' 
            AND COLUMN_NAME = 'election'
            """
            
            result = connection.execute(text(check_column_sql))
            column_exists = result.fetchone() is not None
            
            if not column_exists:
                # Add the missing election column
                add_column_sql = """
                ALTER TABLE packing_lists 
                ADD COLUMN election VARCHAR(255) NOT NULL DEFAULT 'General Election 2024'
                """
                
                connection.execute(text(add_column_sql))
                connection.commit()
                logger.info("✅ Added missing 'election' column to packing_lists table")
            else:
                logger.info("✅ 'election' column already exists in packing_lists table")
                
    except Exception as e:
        logger.error(f"❌ Error fixing packing_lists table: {e}")
        raise e
    finally:
        engine.dispose()

if __name__ == "__main__":
    fix_packing_lists_table()
    print("Migration completed successfully!") 