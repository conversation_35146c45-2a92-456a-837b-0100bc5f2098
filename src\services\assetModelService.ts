// Frontend Asset Model Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface AssetModel {
  id: string;
  modelName: string;
  assetType: string;
  manufacturer: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AssetModelCreationData {
  modelName: string;
  assetType: string;
  manufacturer: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class AssetModelService {
  // Get all asset models
  async getAllAssetModels(): Promise<{ assetModels: AssetModel[]; count: number }> {
    try {
      const response = await apiCall('/asset-models');
      if (response.success) {
        return {
          assetModels: response.data.assetModels,
          count: response.data.count
        };
      }
      throw new Error(response.message || 'Failed to fetch asset models');
    } catch (error) {
      console.error('Error fetching asset models:', error);
      throw error;
    }
  }

  // Get asset model by ID
  async getAssetModelById(id: string): Promise<AssetModel> {
    try {
      const response = await apiCall(`/asset-models/${id}`);
      if (response.success) {
        return response.data.assetModel;
      }
      throw new Error(response.message || 'Failed to fetch asset model');
    } catch (error) {
      console.error('Error fetching asset model by ID:', error);
      throw error;
    }
  }

  // Create new asset model
  async createAssetModel(assetModelData: AssetModelCreationData): Promise<AssetModel> {
    try {
      const response = await apiCall('/asset-models', {
        method: 'POST',
        body: JSON.stringify(assetModelData),
      });
      if (response.success) {
        return response.data.assetModel;
      }
      throw new Error(response.message || 'Failed to create asset model');
    } catch (error) {
      console.error('Error creating asset model:', error);
      throw error;
    }
  }

  // Update asset model
  async updateAssetModel(id: string, assetModelData: Partial<AssetModelCreationData>): Promise<AssetModel> {
    try {
      const response = await apiCall(`/asset-models/${id}`, {
        method: 'PUT',
        body: JSON.stringify(assetModelData),
      });
      if (response.success) {
        return response.data.assetModel;
      }
      throw new Error(response.message || 'Failed to update asset model');
    } catch (error) {
      console.error('Error updating asset model:', error);
      throw error;
    }
  }

  // Delete asset model
  async deleteAssetModel(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/asset-models/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting asset model:', error);
      throw error;
    }
  }

  // Helper method to prepare asset models for export
  prepareAssetModelsForExport(assetModels: AssetModel[]) {
    return assetModels.map(assetModel => ({
      ID: assetModel.id,
      'Model Name': assetModel.modelName,
      'Asset Type': assetModel.assetType,
      'Manufacturer': assetModel.manufacturer,
      'Status': assetModel.status ? 'Active' : 'Inactive',
      'Created At': new Date(assetModel.createdAt).toLocaleDateString(),
      'Updated At': new Date(assetModel.updatedAt).toLocaleDateString()
    }));
  }
}

const assetModelService = new AssetModelService();
export default assetModelService;
