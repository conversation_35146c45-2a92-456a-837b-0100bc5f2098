// Asset Service - Frontend API service for asset management
import {
  AssetStatus,
  WorkflowModule,
  AssetStatusTransitionRequest,
  AssetStatusTransitionResponse,
  ValidTransitionsResponse,
  AssetStatusHistoryResponse
} from '../types/workflow';
import { workflowService } from './workflowService';

// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface Asset {
  id: number;
  assetId: string;
  name: string;
  assetTypeId: number;
  category: string;
  subcategory?: string;
  model?: string;
  manufacturer?: string;
  serialNumber?: string;
  purchaseDate?: Date;
  purchasePrice?: number;
  warrantyExpiration?: Date;
  description?: string;
  specifications?: string;
  status: 'Available' | 'Checked Out' | 'In Transit' | 'Under Maintenance' | 'Decommissioned' | 'Lost' | 'Damaged';
  condition: 'Excellent' | 'Good' | 'Fair' | 'Poor' | 'Needs Repair';
  location: string;
  assignedTo?: string;
  assignedDate?: Date;
  notes?: string;
  qrCode?: string;
  rfidTag?: string;
  photos?: string;
  documents?: string;
  maintenanceSchedule?: string;
  lastMaintenanceDate?: Date;
  nextMaintenanceDate?: Date;
  acquisitionMethod: 'Purchase' | 'Lease' | 'Donation' | 'Transfer';
  supplier?: string;
  invoiceNumber?: string;
  poNumber?: string;
  fundingSource?: string;
  costCenter?: string;
  depreciationRate?: number;
  currentValue?: number;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AssetFilters {
  status?: string;
  condition?: string;     
  category?: string;
  location?: string;
  assignedTo?: string;
  assetTypeId?: number;
  manufacturer?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
}

export interface AssetStats {
  totalAssets: number;
  availableAssets: number;
  checkedOutAssets: number;
  maintenanceAssets: number;
  statusBreakdown: Array<{ status: string; count: number }>;
  conditionBreakdown: Array<{ condition: string; count: number }>;
  categoryBreakdown: Array<{ category: string; count: number }>;
  locationBreakdown: Array<{ location: string; count: number }>;
  totalValue: number;
  averageValue: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

class AssetService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/assets${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if token exists
        ...(localStorage.getItem('authToken') && {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        })
      }
    };

    const response = await fetch(url, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Get all assets with optional filtering
  async getAssets(filters: AssetFilters = {}): Promise<PaginatedResponse<Asset>> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.makeRequest<PaginatedResponse<Asset>>(endpoint);
  }

  // Get asset by ID
  async getAssetById(id: number): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}`);
  }

  // Get asset by asset ID (string identifier)
  async getAssetByAssetId(assetId: string): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/asset-id/${assetId}`);
  }

  // Create new asset
  async createAsset(asset: Partial<Asset>): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>('', {
      method: 'POST',
      body: JSON.stringify(asset)
    });
  }

  // Update asset
  async updateAsset(id: number, asset: Partial<Asset>): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(asset)
    });
  }

  // Delete asset
  async deleteAsset(id: number): Promise<ApiResponse<void>> {
    return this.makeRequest<ApiResponse<void>>(`/${id}`, {
      method: 'DELETE'
    });
  }

  // Update asset status
  async updateAssetStatus(id: number, status: Asset['status']): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status })
    });
  }

  // Update asset condition
  async updateAssetCondition(id: number, condition: Asset['condition']): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}/condition`, {
      method: 'PATCH',
      body: JSON.stringify({ condition })
    });
  }

  // Assign asset to person
  async assignAsset(id: number, assignedTo: string): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}/assign`, {
      method: 'PATCH',
      body: JSON.stringify({ assignedTo })
    });
  }

  // Unassign asset
  async unassignAsset(id: number): Promise<ApiResponse<Asset>> {
    return this.makeRequest<ApiResponse<Asset>>(`/${id}/unassign`, {
      method: 'PATCH'
    });
  }

  // Get asset statistics
  async getAssetStats(): Promise<ApiResponse<AssetStats>> {
    return this.makeRequest<ApiResponse<AssetStats>>('/stats');
  }

  // Search assets
  async searchAssets(query: string, filters: AssetFilters = {}): Promise<PaginatedResponse<Asset>> {
    const searchFilters = { ...filters, search: query };
    return this.getAssets(searchFilters);
  }

  // Get assets by location
  async getAssetsByLocation(location: string): Promise<ApiResponse<Asset[]>> {
    return this.makeRequest<ApiResponse<Asset[]>>(`/location/${encodeURIComponent(location)}`);
  }

  // Get assets by category
  async getAssetsByCategory(category: string): Promise<ApiResponse<Asset[]>> {
    return this.makeRequest<ApiResponse<Asset[]>>(`/category/${encodeURIComponent(category)}`);
  }

  // Get maintenance due assets
  async getMaintenanceDueAssets(): Promise<ApiResponse<Asset[]>> {
    return this.makeRequest<ApiResponse<Asset[]>>('/maintenance/due');
  }

  // Get asset value summary
  async getAssetValueSummary(): Promise<ApiResponse<{
    totalValue: number;
    averageValue: number;
    categoryBreakdown: Array<{ category: string; totalValue: number; count: number }>;
  }>> {
    return this.makeRequest<ApiResponse<{
      totalValue: number;
      averageValue: number;
      categoryBreakdown: Array<{ category: string; totalValue: number; count: number }>;
    }>>('/value-summary');
  }

  // =================== WORKFLOW INTEGRATION METHODS ===================

  // Get available workflow transitions for an asset
  async getAssetWorkflowTransitions(assetId: number): Promise<ValidTransitionsResponse> {
    return workflowService.getValidTransitions(assetId);
  }

  // Execute workflow transition
  async executeWorkflowTransition(
    assetId: number,
    newStatus: AssetStatus,
    module: WorkflowModule,
    context?: Record<string, any>,
    notes?: string
  ): Promise<AssetStatusTransitionResponse> {
    return workflowService.transitionAsset({
      assetId,
      newStatus,
      module,
      context,
      notes
    });
  }

  // Get asset status history
  async getAssetStatusHistory(assetId: number): Promise<AssetStatusHistoryResponse[]> {
    const response = await workflowService.getAssetStatusHistory(assetId);
    return response.data || [];
  }

  // Validate workflow action
  async validateWorkflowAction(
    assetId: number,
    targetStatus: AssetStatus,
    module: WorkflowModule,
    context?: Record<string, any>
  ) {
    return workflowService.validateWorkflow({
      assetId,
      targetStatus,
      module,
      context
    });
  }

  // =================== WORKFLOW HELPER METHODS ===================

  // Configure asset (NEW -> CONFIGURED)
  async configureAsset(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.executeWorkflowTransition(
      assetId,
      AssetStatus.CONFIGURED,
      WorkflowModule.MANUAL,
      undefined,
      notes
    );
  }

  // Complete L&A checklist (CONFIGURED -> READY)
  async completeLA(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.completeLA(assetId, notes);
  }

  // Pack asset (READY -> PACKED)
  async packAsset(assetId: number, packingListId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.packAsset(assetId, packingListId, notes);
  }

  // Unpack asset (PACKED -> READY)
  async unpackAsset(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.unpackAsset(assetId, notes);
  }

  // Check out asset (READY/PACKED -> CHECKED_OUT)
  async checkoutAsset(assetId: number, sessionId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.checkoutAsset(assetId, sessionId, notes);
  }

  // Check in asset (CHECKED_OUT -> READY)
  async checkinAsset(assetId: number, sessionId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.checkinAsset(assetId, sessionId, notes);
  }

  // Start maintenance (any status -> MAINTENANCE)
  async startMaintenance(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.startMaintenance(assetId, notes);
  }

  // Complete maintenance (MAINTENANCE -> READY)
  async completeMaintenance(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.completeMaintenance(assetId, notes);
  }

  // Report damage (any status -> DAMAGED)
  async reportDamage(assetId: number, reportId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return workflowService.reportDamage(assetId, reportId, notes);
  }

  // Transfer asset (varies based on transfer type)
  async transferAsset(
    assetId: number,
    transferId?: number,
    targetStatus?: AssetStatus,
    notes?: string
  ): Promise<AssetStatusTransitionResponse> {
    return workflowService.transferAsset(assetId, transferId, targetStatus, notes);
  }

  // =================== BATCH OPERATIONS ===================

  // Batch workflow transitions
  async batchWorkflowTransition(
    requests: AssetStatusTransitionRequest[]
  ): Promise<AssetStatusTransitionResponse[]> {
    return workflowService.batchTransitionAssets(requests);
  }

  // Batch pack assets
  async batchPackAssets(assetIds: number[], packingListId?: number): Promise<AssetStatusTransitionResponse[]> {
    const requests = assetIds.map(assetId => ({
      assetId,
      newStatus: AssetStatus.PACKED,
      module: WorkflowModule.PACKING,
      context: packingListId ? { packingListId } : undefined
    }));
    return this.batchWorkflowTransition(requests);
  }

  // Batch unpack assets
  async batchUnpackAssets(assetIds: number[]): Promise<AssetStatusTransitionResponse[]> {
    const requests = assetIds.map(assetId => ({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.PACKING
    }));
    return this.batchWorkflowTransition(requests);
  }

  // Batch checkout assets
  async batchCheckoutAssets(assetIds: number[], sessionId?: number): Promise<AssetStatusTransitionResponse[]> {
    const requests = assetIds.map(assetId => ({
      assetId,
      newStatus: AssetStatus.CHECKED_OUT,
      module: WorkflowModule.CHECKOUT,
      context: sessionId ? { sessionId } : undefined
    }));
    return this.batchWorkflowTransition(requests);
  }

  // Batch checkin assets
  async batchCheckinAssets(assetIds: number[], sessionId?: number): Promise<AssetStatusTransitionResponse[]> {
    const requests = assetIds.map(assetId => ({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.CHECKOUT,
      context: sessionId ? { sessionId } : undefined
    }));
    return this.batchWorkflowTransition(requests);
  }

  // =================== WORKFLOW FILTERED QUERIES ===================

  // Get assets by workflow status
  async getAssetsByStatus(status: AssetStatus, filters: AssetFilters = {}): Promise<PaginatedResponse<Asset>> {
    return this.getAssets({ ...filters, status });
  }

  // Get assets ready for packing
  async getAssetsReadyForPacking(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.READY);
  }

  // Get packed assets
  async getPackedAssets(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.PACKED);
  }

  // Get checked out assets
  async getCheckedOutAssets(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.CHECKED_OUT);
  }

  // Get assets in maintenance
  async getAssetsInMaintenance(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.MAINTENANCE);
  }

  // Get damaged assets
  async getDamagedAssets(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.DAMAGED);
  }

  // Get new assets requiring configuration
  async getNewAssets(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.NEW);
  }

  // Get configured assets ready for L&A
  async getConfiguredAssets(): Promise<PaginatedResponse<Asset>> {
    return this.getAssetsByStatus(AssetStatus.CONFIGURED);
  }
}

export const assetService = new AssetService();
export default assetService;
