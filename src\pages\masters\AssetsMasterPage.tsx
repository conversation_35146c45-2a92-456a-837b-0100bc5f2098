import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Pencil, Trash2, Eye, Search, Plus, AlertTriangle } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Breadcrumb } from "@/components/layout/Breadcrumb";
import { toast } from "@/components/ui/use-toast";
import assetService, { Asset } from "@/services/assetService";

const mockAssets = [
  { 
    id: 1, 
    tag: "SCE-001",
    ownedBy: "Charleston County",
    category: "Scanner",
    model: "DS200",
    name: "Ballot Scanner",
    purchaseCost: 5499.99,
    status: "Active",
    lastUpdated: "2025-05-20"
  },  { 
    id: 2, 
    tag: "SCE-002",
    ownedBy: "Richland County",
    category: "Tablet",
    model: "PP2.5",
    name: "Poll Pad",
    purchaseCost: 899.99,
    status: "Active",
    lastUpdated: "2025-05-21"
  },  { 
    id: 3, 
    tag: "SCE-003",
    ownedBy: "Greenville County",
    category: "Printer",
    model: "HP LaserJet Pro",
    name: "BMD Printer",
    purchaseCost: 449.99,
    status: "Maintenance",
    lastUpdated: "2025-05-22"
  },
];

export default function AssetsMasterPage() {
  const handleDelete = (assetId: number) => {
    // In a real application, you would call an API to delete the asset
    toast({
      title: "Asset Deleted",
      description: `Asset ${assetId} has been deleted successfully.`,
    });
  };

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <h1 className="text-3xl font-bold">Assets Master</h1>
        <Card>
          <CardHeader>
            <CardTitle>Election Assets</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>              <TableHeader>
                <TableRow>
                  <TableHead>Tag</TableHead>
                  <TableHead>Owned By</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Model</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead className="text-right">Purchase Cost</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockAssets.map((asset) => (                  <TableRow key={asset.id}>
                    <TableCell>{asset.tag}</TableCell>
                    <TableCell>{asset.ownedBy}</TableCell>
                    <TableCell>{asset.category}</TableCell>                    <TableCell>{asset.model}</TableCell>
                    <TableCell>{asset.name}</TableCell>
                    <TableCell className="text-right font-medium">${asset.purchaseCost.toLocaleString()}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        asset.status === 'Active' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-yellow-100 text-yellow-700'
                      }`}>
                        {asset.status}                      </span>
                    </TableCell>
                    <TableCell>{asset.lastUpdated}</TableCell>
                    <TableCell className="text-right space-x-2">
                      <Button variant="ghost" size="icon" title="View Asset">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" title="Edit Asset">
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="icon" className="text-red-600 hover:text-red-700 hover:bg-red-50">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Asset</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this asset? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => handleDelete(asset.id)}
                              className="bg-red-600 hover:bg-red-700 text-white"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
