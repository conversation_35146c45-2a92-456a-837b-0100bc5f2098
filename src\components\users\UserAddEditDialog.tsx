import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Backend User interface
interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  userGroup: string;
  loginEnabled: boolean;
  loginId: string;
  accessLevel: 'county' | 'state' | 'precinct';
  state?: string;
  county?: string;
  precinct?: string;
  status: boolean;
  image?: string;
  company?: string;
  employeeNo?: string;
  manager?: string;
  department?: string;
  location?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  role: 'admin' | 'manager' | 'user' | 'Portal Admin';
  username?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserCreationData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  mobile: string;
  userGroup: string;
  loginEnabled: boolean;
  loginId: string;
  accessLevel: 'county' | 'state' | 'precinct';
  state?: string;
  county?: string;
  precinct?: string;
  status: boolean;
  image?: string;
  company?: string;
  employeeNo?: string;
  manager?: string;
  department?: string;
  location?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  role?: 'admin' | 'manager' | 'user' | 'Portal Admin';
  username?: string;
  phone?: string;
}

interface UserAddEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User;
  onSave: (userData: UserCreationData) => void;
  isEditMode: boolean;
}

// Mock user groups - you can replace this with actual API call
const mockUserGroups = [
  "Admin Group",
  "Manager Group", 
  "User Group",
  "County Admin",
  "State Admin"
];

export function UserAddEditDialog({
  open,
  onOpenChange,
  user,
  onSave,
  isEditMode
}: UserAddEditDialogProps) {
  const [formData, setFormData] = useState<UserCreationData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    mobile: '',
    userGroup: '',
    loginEnabled: true,
    loginId: '',
    accessLevel: 'county',
    county: '',
    precinct: '',
    status: true,
    image: '',
    company: '',
    employeeNo: '',
    manager: '',
    department: '',
    location: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    pincode: '',
    country: '',
    role: 'user',
    username: '',
    phone: ''
  });

  // RBAC validation state
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const [userGroups] = useState<string[]>(mockUserGroups);

  // RBAC validation function
  const validateGeographicalScope = (data: UserCreationData): string[] => {
    const errors: string[] = [];

    if (data.accessLevel === 'state') {
      if (!data.state) {
        errors.push('State is required for state-level access');
      }
    } else if (data.accessLevel === 'county') {
      if (!data.state) {
        errors.push('State is required for county-level access');
      }
      if (!data.county) {
        errors.push('County is required for county-level access');
      }
    } else if (data.accessLevel === 'precinct') {
      if (!data.state) {
        errors.push('State is required for precinct-level access');
      }
      if (!data.county) {
        errors.push('County is required for precinct-level access');
      }
      if (!data.precinct) {
        errors.push('Precinct is required for precinct-level access');
      }
    }

    return errors;
  };

  // Initialize form data when editing an existing user
  useEffect(() => {
    if (isEditMode && user) {
      setFormData({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        password: '', // Don't pre-fill password for security
        mobile: user.mobile,
        userGroup: user.userGroup,
        loginEnabled: user.loginEnabled,
        loginId: user.loginId,
        accessLevel: user.accessLevel,
        state: user.state || '',
        county: user.county || '',
        precinct: user.precinct || '',
        status: user.status,
        image: user.image || '',
        company: user.company || '',
        employeeNo: user.employeeNo || '',
        manager: user.manager || '',
        department: user.department || '',
        location: user.location || '',
        addressLine1: user.addressLine1 || '',
        addressLine2: user.addressLine2 || '',
        city: user.city || '',
        state: user.state || '',
        pincode: user.pincode || '',
        country: user.country || '',
        role: user.role,
        username: user.username || '',
        phone: user.phone || ''
      });
    } else {
      // Reset form when adding a new user
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        mobile: '',
        userGroup: '',
        loginEnabled: true,
        loginId: '',
        accessLevel: 'county',
        county: '',
        status: true,
        image: '',
        company: '',
        employeeNo: '',
        manager: '',
        department: '',
        location: '',
        addressLine1: '',
        addressLine2: '',
        city: '',
        state: '',
        pincode: '',
        country: '',
        role: 'user',
        username: '',
        phone: ''
      });
    }
  }, [isEditMode, user, open]);

  // Handle input changes
  const handleInputChange = (field: keyof UserCreationData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.firstName.trim()) {
      alert("First Name is required");
      return;
    }
    if (!formData.lastName.trim()) {
      alert("Last Name is required");
      return;
    }
    if (!formData.email.trim()) {
      alert("Email is required");
      return;
    }
    if (!formData.mobile.trim()) {
      alert("Mobile is required");
      return;
    }
    if (!formData.userGroup.trim()) {
      alert("User Group is required");
      return;
    }
    if (!formData.loginId.trim()) {
      alert("Login ID is required");
      return;
    }
    if (!isEditMode && !formData.password.trim()) {
      alert("Password is required for new users");
      return;
    }

    // RBAC geographical scope validation
    const geoErrors = validateGeographicalScope(formData);
    if (geoErrors.length > 0) {
      setValidationErrors(geoErrors);
      alert("Please fix the following errors:\n" + geoErrors.join("\n"));
      return;
    }

    // Clear validation errors if all is good
    setValidationErrors([]);

    // For edit mode, don't send password if it's empty
    const dataToSend = { ...formData };
    if (isEditMode && !dataToSend.password.trim()) {
      delete dataToSend.password;
    }

    onSave(dataToSend);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditMode ? "Edit User" : "Add User"}
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="firstName" className="text-right">
                First Name *
              </Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => handleInputChange("firstName", e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="lastName" className="text-right">
                Last Name *
              </Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => handleInputChange("lastName", e.target.value)}
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email *
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Password {!isEditMode && "*"}
              </Label>
              <Input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className="col-span-3"
                placeholder={isEditMode ? "Leave empty to keep current password" : ""}
                required={!isEditMode}
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="mobile" className="text-right">
                Mobile *
              </Label>
              <Input
                id="mobile"
                value={formData.mobile}
                onChange={(e) => handleInputChange("mobile", e.target.value)}
                className="col-span-3"
                placeholder="e.g. (*************"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                Phone
              </Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="col-span-3"
                placeholder="e.g. (*************"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="userGroup" className="text-right">
                User Group *
              </Label>
              <Select
                value={formData.userGroup}
                onValueChange={(value) => handleInputChange("userGroup", value)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select user group" />
                </SelectTrigger>
                <SelectContent>
                  {userGroups.map((group) => (
                    <SelectItem key={group} value={group}>
                      {group}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="loginId" className="text-right">
                Login ID *
              </Label>
              <Input
                id="loginId"
                value={formData.loginId}
                onChange={(e) => handleInputChange("loginId", e.target.value)}
                className="col-span-3"
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Role
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value) => handleInputChange("role", value as any)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="Portal Admin">Portal Admin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="accessLevel" className="text-right">
                Access Level *
              </Label>
              <Select
                value={formData.accessLevel}
                onValueChange={(value) => handleInputChange("accessLevel", value as 'county' | 'state' | 'precinct')}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select access level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="state">State Level</SelectItem>
                  <SelectItem value="county">County Level</SelectItem>
                  <SelectItem value="precinct">Precinct Level</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Conditional geographical fields based on access level */}
            {(formData.accessLevel === 'state' || formData.accessLevel === 'county' || formData.accessLevel === 'precinct') && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="state" className="text-right">
                  State *
                </Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  className="col-span-3"
                  placeholder="Enter state name"
                />
              </div>
            )}

            {(formData.accessLevel === 'county' || formData.accessLevel === 'precinct') && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="county" className="text-right">
                  County *
                </Label>
                <Input
                  id="county"
                  value={formData.county}
                  onChange={(e) => handleInputChange("county", e.target.value)}
                  className="col-span-3"
                  placeholder="Enter county name"
                />
              </div>
            )}

            {formData.accessLevel === 'precinct' && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="precinct" className="text-right">
                  Precinct *
                </Label>
                <Input
                  id="precinct"
                  value={formData.precinct}
                  onChange={(e) => handleInputChange("precinct", e.target.value)}
                  className="col-span-3"
                  placeholder="Enter precinct name"
                />
              </div>
            )}

            {/* Display validation errors */}
            {validationErrors.length > 0 && (
              <div className="col-span-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm font-medium text-red-800 mb-2">Please fix the following errors:</p>
                <ul className="text-sm text-red-700 list-disc list-inside">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="company" className="text-right">
                Company
              </Label>
              <Input
                id="company"
                value={formData.company}
                onChange={(e) => handleInputChange("company", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="employeeNo" className="text-right">
                Employee No.
              </Label>
              <Input
                id="employeeNo"
                value={formData.employeeNo}
                onChange={(e) => handleInputChange("employeeNo", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="manager" className="text-right">
                Manager
              </Label>
              <Input
                id="manager"
                value={formData.manager}
                onChange={(e) => handleInputChange("manager", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="department" className="text-right">
                Department
              </Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleInputChange("department", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                Location
              </Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="addressLine1" className="text-right">
                Address Line1
              </Label>
              <Input
                id="addressLine1"
                value={formData.addressLine1}
                onChange={(e) => handleInputChange("addressLine1", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="addressLine2" className="text-right">
                Address Line2
              </Label>
              <Input
                id="addressLine2"
                value={formData.addressLine2}
                onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="city" className="text-right">
                City
              </Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="state" className="text-right">
                State
              </Label>
              <Input
                id="state"
                value={formData.state}
                onChange={(e) => handleInputChange("state", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="pincode" className="text-right">
                Pincode
              </Label>
              <Input
                id="pincode"
                value={formData.pincode}
                onChange={(e) => handleInputChange("pincode", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="country" className="text-right">
                Country
              </Label>
              <Input
                id="country"
                value={formData.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="loginEnabled" className="text-right">
                Login Enabled
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="loginEnabled"
                  checked={formData.loginEnabled}
                  onCheckedChange={(checked) => handleInputChange("loginEnabled", checked)}
                />
                <span>{formData.loginEnabled ? "Enabled" : "Disabled"}</span>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="status"
                  checked={formData.status}
                  onCheckedChange={(checked) => handleInputChange("status", checked)}
                />
                <span>{formData.status ? "Active" : "Inactive"}</span>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">
              {isEditMode ? "Save Changes" : "Add User"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}