# app/routes/transaction_order.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, and_
from app.config.database import get_db
from app.models.transaction_orders import TransactionOrder, OrderStatus
from app.models.consumables_category import ConsumablesCategory
from app.models.packing_location import PackingLocation
from app.models.masters.location import Location
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from app.schemas.transaction_orders import (
    TransactionOrderCreate, TransactionOrderUpdate, TransactionOrderResponse,
    TransactionOrderStatusUpdate, TransactionOrderSummary
)
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from uuid import uuid4
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=dict)
async def get_transaction_orders(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    order_status: Optional[str] = Query(None, alias="status"),
    date_from: Optional[date] = Query(None, alias="dateFrom"),
    date_to: Optional[date] = Query(None, alias="dateTo"),
    vendor: Optional[str] = Query(None),
    location: Optional[str] = Query(None),
    consumable: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all transaction orders with filtering and pagination.
    Equivalent to GET / in Node.js version.
    """
    try:
        offset = (page - 1) * limit
        query = db.query(TransactionOrder)
        
        # Search filter
        if search:
            search_filter = or_(
                TransactionOrder.id.ilike(f"%{search}%"),
                TransactionOrder.vendor.ilike(f"%{search}%"),
                TransactionOrder.consumable.ilike(f"%{search}%"),
                TransactionOrder.ref_no == int(search) if search.isdigit() else False
            )
            query = query.filter(search_filter)
        
        # Status filter
        if order_status and order_status != 'all':
            query = query.filter(TransactionOrder.status == order_status)
        
        # Date range filter
        if date_from:
            query = query.filter(TransactionOrder.date >= date_from)
        if date_to:
            query = query.filter(TransactionOrder.date <= date_to)
        
        # Vendor filter
        if vendor:
            query = query.filter(TransactionOrder.vendor.ilike(f"%{vendor}%"))
        
        # Location filter
        if location:
            query = query.filter(TransactionOrder.location == location)
        
        # Consumable filter
        if consumable:
            query = query.filter(TransactionOrder.consumable.ilike(f"%{consumable}%"))
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering first, then pagination
        orders = query.order_by(TransactionOrder.created_at.desc()).offset(offset).limit(limit).all()
        
        # Get location names for the orders
        orders_with_location_names = []
        for order in orders:
            order_dict = order.to_dict()
            location_name = order.location  # Default to ID
            
            try:
                # Try PackingLocation first
                packing_location = db.query(PackingLocation).filter(PackingLocation.id == order.location).first()
                if packing_location:
                    location_name = packing_location.name
                else:
                    # Try masters Location
                    master_location = db.query(Location).filter(Location.id == order.location).first()
                    if master_location:
                        location_name = master_location.name
            except Exception as e:
                logger.warning(f"Could not find location name for ID: {order.location}")
            
            order_dict["locationName"] = location_name
            orders_with_location_names.append(order_dict)
        
        return {
            "success": True,
            "data": {
                "orders": orders_with_location_names
            },
            "pagination": {
                "total": total_count,
                "page": page,
                "limit": limit,
                "pages": (total_count + limit - 1) // limit
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching transaction orders: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching transaction orders"
        )

@router.get("/summary", response_model=dict)
async def get_order_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get order summary statistics.
    Equivalent to GET /summary in Node.js version.
    """
    try:
        current_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        next_month = datetime(current_month.year + (current_month.month // 12), 
                            ((current_month.month % 12) + 1), 1)
        
        total_orders = db.query(TransactionOrder).count()
        pending_orders = db.query(TransactionOrder).filter(TransactionOrder.status == OrderStatus.PENDING).count()
        in_transit_orders = db.query(TransactionOrder).filter(TransactionOrder.status == OrderStatus.APPROVED).count()
        orders_this_month = db.query(TransactionOrder).filter(
            and_(TransactionOrder.created_at >= current_month,
                 TransactionOrder.created_at < next_month)
        ).count()
        
        return {
            "success": True,
            "data": {
                "totalOrders": total_orders,
                "pendingOrders": pending_orders,
                "inTransitOrders": in_transit_orders,
                "ordersThisMonth": orders_this_month
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching order summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching order summary"
        )

@router.get("/consumable-summary", response_model=dict)
async def get_consumable_summary(
    consumable: Optional[str] = Query(None),
    date_from: Optional[date] = Query(None, alias="dateFrom"),
    date_to: Optional[date] = Query(None, alias="dateTo"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get consumable summary statistics.
    Equivalent to GET /consumable-summary in Node.js version.
    """
    try:
        query = db.query(TransactionOrder).filter(TransactionOrder.status == OrderStatus.COMPLETED)
        
        if consumable:
            query = query.filter(TransactionOrder.consumable.ilike(f"%{consumable}%"))
        
        if date_from:
            query = query.filter(TransactionOrder.date >= date_from)
        if date_to:
            query = query.filter(TransactionOrder.date <= date_to)
        
        orders = query.all()
        
        consumable_summary = {}
        
        for order in orders:
            consumable_name = order.consumable
            if consumable_name not in consumable_summary:
                consumable_summary[consumable_name] = {
                    "totalOrders": 0,
                    "totalAmount": 0,
                    "totalQty": 0,
                    "totalItems": 0,
                    "locations": set(),
                    "items": {}
                }
            
            consumable_summary[consumable_name]["totalOrders"] += 1
            consumable_summary[consumable_name]["totalAmount"] += float(order.total)
            consumable_summary[consumable_name]["totalQty"] += order.qty
            consumable_summary[consumable_name]["locations"].add(order.location)
            
            items = order.get_items_data()
            for item in items:
                item_key = item["item"]
                if item_key not in consumable_summary[consumable_name]["items"]:
                    consumable_summary[consumable_name]["items"][item_key] = {
                        "totalQty": 0,
                        "totalAmount": 0
                    }
                consumable_summary[consumable_name]["items"][item_key]["totalQty"] += item["qty"]
                consumable_summary[consumable_name]["items"][item_key]["totalAmount"] += item["amount"]
                consumable_summary[consumable_name]["totalItems"] += item["qty"]
        
        # Convert sets to lists
        for key in consumable_summary:
            consumable_summary[key]["locations"] = list(consumable_summary[key]["locations"])
        
        return {
            "success": True,
            "data": consumable_summary
        }
        
    except Exception as e:
        logger.error(f"Error fetching consumable summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching consumable summary"
        )

@router.get("/stock-summary", response_model=dict)
async def get_stock_summary(
    location: Optional[str] = Query(None),
    item: Optional[str] = Query(None),
    consumable: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get stock summary by location and item.
    Equivalent to GET /stock-summary in Node.js version.
    """
    try:
        query = db.query(TransactionOrder).filter(TransactionOrder.status == OrderStatus.COMPLETED)
        
        if location:
            query = query.filter(TransactionOrder.location == location)
        
        if consumable:
            query = query.filter(TransactionOrder.consumable.ilike(f"%{consumable}%"))
        
        orders = query.all()
        
        stock_summary = {}
        
        for order in orders:
            location_key = f"{order.location} - {order.consumable}"
            if location_key not in stock_summary:
                stock_summary[location_key] = {
                    "orderQty": 0
                }
            
            stock_summary[location_key]["orderQty"] += order.qty
            
            items = order.get_items_data()
            for order_item in items:
                if not item or order_item["item"] == item:
                    item_key = f"{order_item['item']} - {order_item['consumable']}"
                    if item_key not in stock_summary[location_key]:
                        stock_summary[location_key][item_key] = {
                            "item": order_item["item"],
                            "consumable": order_item["consumable"],
                            "location": order.location,
                            "orderConsumable": order.consumable,
                            "totalQty": 0,
                            "totalAmount": 0
                        }
                    stock_summary[location_key][item_key]["totalQty"] += order_item["qty"]
                    stock_summary[location_key][item_key]["totalAmount"] += order_item["amount"]
        
        return {
            "success": True,
            "data": stock_summary
        }
        
    except Exception as e:
        logger.error(f"Error fetching stock summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching stock summary"
        )

@router.get("/{order_id}", response_model=dict)
async def get_transaction_order(
    order_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get single transaction order by ID.
    Equivalent to GET /:id in Node.js version.
    """
    try:
        order = db.query(TransactionOrder).filter(TransactionOrder.id == order_id).first()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction order not found"
            )
        
        # Get location name
        location_name = order.location
        try:
            packing_location = db.query(PackingLocation).filter(PackingLocation.id == order.location).first()
            if packing_location:
                location_name = packing_location.name
            else:
                master_location = db.query(Location).filter(Location.id == order.location).first()
                if master_location:
                    location_name = master_location.name
        except Exception as e:
            logger.warning(f"Could not find location name for ID: {order.location}")
        
        order_dict = order.to_dict()
        order_dict["locationName"] = location_name
        
        return {
            "success": True,
            "data": order_dict
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching transaction order: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching transaction order"
        )

@router.post("/", response_model=dict)
async def create_transaction_order(
    order_data: TransactionOrderCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create new transaction order.
    Equivalent to POST / in Node.js version.
    """
    try:
        # Validate required fields
        if not all([order_data.vendor, order_data.date, order_data.location, 
                   order_data.consumable, order_data.qty, order_data.items]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required fields: vendor, date, location, consumable, qty, and items are required"
            )
        
        # Validate qty
        if order_data.qty <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="qty must be a positive number"
            )
        
        # Create new order
        new_order = TransactionOrder(
            id=str(uuid4()),
            vendor=order_data.vendor.strip(),
            date=order_data.date,
            location=order_data.location,
            note=order_data.note.strip() if order_data.note else None,
            consumable=order_data.consumable.strip(),
            qty=order_data.qty,
            status=order_data.status or OrderStatus.PENDING
        )
        
        # Set items and calculate totals
        new_order.set_items_data(order_data.items)
        
        # Save to database
        db.add(new_order)
        db.commit()
        db.refresh(new_order)
        
        return {
            "success": True,
            "message": "Transaction order created successfully",
            "data": new_order.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating transaction order: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error creating transaction order"
        )

@router.put("/{order_id}", response_model=dict)
async def update_transaction_order(
    order_id: str,
    order_data: TransactionOrderUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update transaction order.
    Equivalent to PUT /:id in Node.js version.
    """
    try:
        order = db.query(TransactionOrder).filter(TransactionOrder.id == order_id).first()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction order not found"
            )
        
        # Validate qty if being updated
        if order_data.qty is not None and order_data.qty <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="qty must be a positive number"
            )
        
        # Update fields
        update_data = order_data.dict(exclude_unset=True)
        
        # Handle items separately
        if 'items' in update_data:
            items = update_data.pop('items')
            if items is not None:
                order.set_items_data(items)
        
        # Update other fields
        for field, value in update_data.items():
            if hasattr(order, field) and value is not None:
                if isinstance(value, str):
                    setattr(order, field, value.strip())
                else:
                    setattr(order, field, value)
        
        db.commit()
        db.refresh(order)
        
        return {
            "success": True,
            "message": "Transaction order updated successfully",
            "data": order.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating transaction order: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error updating transaction order"
        )

@router.patch("/{order_id}/status", response_model=dict)
async def update_order_status(
    order_id: str,
    status_data: TransactionOrderStatusUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update order status.
    Equivalent to PATCH /:id/status in Node.js version.
    """
    try:
        order = db.query(TransactionOrder).filter(TransactionOrder.id == order_id).first()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction order not found"
            )
        
        order.status = status_data.status
        db.commit()
        db.refresh(order)
        
        return {
            "success": True,
            "message": "Order status updated successfully",
            "data": order.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating order status: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Error updating order status"
        )

@router.delete("/{order_id}")
async def delete_transaction_order(
    order_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete transaction order.
    Equivalent to DELETE /:id in Node.js version.
    """
    try:
        order = db.query(TransactionOrder).filter(TransactionOrder.id == order_id).first()
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Transaction order not found"
            )
        
        db.delete(order)
        db.commit()
        
        return {
            "success": True,
            "message": "Transaction order deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting transaction order: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting transaction order"
        )

@router.delete("/")
async def bulk_delete_transaction_orders(
    delete_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Bulk delete transaction orders.
    Equivalent to DELETE / in Node.js version.
    """
    try:
        ids = delete_data.get("ids", [])
        
        if not ids or not isinstance(ids, list) or len(ids) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or missing ids array"
            )
        
        deleted_count = db.query(TransactionOrder).filter(TransactionOrder.id.in_(ids)).delete(synchronize_session=False)
        db.commit()
        
        return {
            "success": True,
            "message": f"{deleted_count} transaction orders deleted successfully",
            "deletedCount": deleted_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting transaction orders: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting transaction orders"
        )

# Configuration endpoints
@router.get("/config/consumable-categories", response_model=dict)
async def get_consumable_categories(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get consumable categories for item dropdown."""
    try:
        categories = db.query(ConsumablesCategory).order_by(ConsumablesCategory.name).all()
        return {
            "success": True,
            "data": [{"id": cat.id, "name": cat.name} for cat in categories]
        }
    except Exception as e:
        logger.error(f"Error fetching consumable categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching consumable categories"
        )

@router.get("/config/packing-locations", response_model=dict)
async def get_packing_locations(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get packing locations for location dropdown."""
    try:
        locations = db.query(PackingLocation).order_by(PackingLocation.name).all()
        return {
            "success": True,
            "data": [{"id": loc.id, "name": loc.name} for loc in locations]
        }
    except Exception as e:
        logger.error(f"Error fetching packing locations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching packing locations"
        )

@router.get("/config/vendors", response_model=dict)
async def get_vendors_list(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get distinct vendors for dropdown."""
    try:
        vendors = db.query(TransactionOrder.vendor).distinct().order_by(TransactionOrder.vendor).all()
        return {
            "success": True,
            "data": [{"name": vendor[0]} for vendor in vendors if vendor[0]]
        }
    except Exception as e:
        logger.error(f"Error fetching vendors: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching vendors"
        )

@router.get("/config/consumables", response_model=dict)
async def get_consumables_list(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get distinct consumables for dropdown."""
    try:
        consumables = db.query(TransactionOrder.consumable).distinct().order_by(TransactionOrder.consumable).all()
        return {
            "success": True,
            "data": [{"name": consumable[0]} for consumable in consumables if consumable[0]]
        }
    except Exception as e:
        logger.error(f"Error fetching consumables: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching consumables"
        )

@router.get("/recent", response_model=List[dict])
async def get_recent_consumable_transactions(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get recent consumable transactions for dashboard display."""
    try:
        recent_orders = db.query(TransactionOrder).order_by(
            TransactionOrder.created_at.desc()
        ).limit(limit).all()

        transactions = []
        for order in recent_orders:
            transactions.append({
                "id": order.id,
                "item": order.consumable,
                "consumable_name": order.consumable,
                "type": "Order",
                "quantity": order.qty,
                "date": order.created_at.isoformat() if order.created_at else None,
                "status": order.status.value if order.status else "Unknown",
                "location": order.location,
                "vendor": order.vendor
            })

        return transactions

    except Exception as e:
        logger.error(f"Error getting recent consumable transactions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get recent consumable transactions"
        )