// Frontend Masters Consumables Service (Individual Items)
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface MastersConsumable {
  id: string;
  itemNo: string;
  name: string;
  description?: string;
  categoryId: string;
  unit: string;
  minQty: number;
  maxQty: number;
  unitCost: number;
  vendor?: string;
  status: boolean;
  isCritical: boolean;
  leadTimeDays: number;
  properties: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
    status: boolean;
  };
  // Stock information (added by backend)
  totalStock?: number;
  availableStock?: number;
  isLowStock?: boolean;
  isOutOfStock?: boolean;
}

export interface MastersConsumableCreationData {
  itemNo: string;
  name: string;
  description?: string;
  categoryId: string;
  unit?: string;
  minQty?: number;
  maxQty?: number;
  unitCost?: number;
  vendor?: string;
  status?: boolean;
  isCritical?: boolean;
  leadTimeDays?: number;
  properties?: Record<string, any>;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class MastersConsumablesService {
  // Get all consumables
  async getAllConsumables(params?: {
    page?: number;
    limit?: number;
    search?: string;
    categoryId?: string;
    status?: boolean;
    isCritical?: boolean;
  }): Promise<{ consumables: MastersConsumable[]; pagination: any }> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.categoryId) queryParams.append('category_id', params.categoryId);
      if (params?.status !== undefined) queryParams.append('status', params.status.toString());
      if (params?.isCritical !== undefined) queryParams.append('is_critical', params.isCritical.toString());

      const queryString = queryParams.toString();
      const endpoint = queryString ? `/masters/consumables/?${queryString}` : `/masters/consumables/`;
      const response = await apiCall(endpoint);
      if (response.success) {
        return {
          consumables: response.data.consumables,
          pagination: response.pagination
        };
      }
      throw new Error(response.message || 'Failed to fetch consumables');
    } catch (error) {
      console.error('Error fetching consumables:', error);
      throw error;
    }
  }

  // Get consumable by ID
  async getConsumableById(id: string): Promise<MastersConsumable> {
    try {
      const response = await apiCall(`/masters/consumables/${id}/`);
      if (response.success) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch consumable');
    } catch (error) {
      console.error('Error fetching consumable by ID:', error);
      throw error;
    }
  }

  // Create new consumable
  async createConsumable(consumableData: MastersConsumableCreationData): Promise<MastersConsumable> {
    try {
      const response = await apiCall('/masters/consumables/', {
        method: 'POST',
        body: JSON.stringify({
          item_no: consumableData.itemNo,
          name: consumableData.name,
          description: consumableData.description,
          category_id: consumableData.categoryId,
          unit: consumableData.unit || 'pieces',
          min_qty: consumableData.minQty || 0,
          max_qty: consumableData.maxQty || 1000,
          unit_cost: consumableData.unitCost || 0,
          vendor: consumableData.vendor,
          status: consumableData.status !== undefined ? consumableData.status : true,
          is_critical: consumableData.isCritical || false,
          lead_time_days: consumableData.leadTimeDays || 7,
          properties: consumableData.properties || {}
        }),
      });
      if (response.success) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to create consumable');
    } catch (error) {
      console.error('Error creating consumable:', error);
      throw error;
    }
  }

  // Update consumable
  async updateConsumable(id: string, consumableData: Partial<MastersConsumableCreationData>): Promise<MastersConsumable> {
    try {
      const updateData: any = {};
      if (consumableData.itemNo) updateData.item_no = consumableData.itemNo;
      if (consumableData.name) updateData.name = consumableData.name;
      if (consumableData.description !== undefined) updateData.description = consumableData.description;
      if (consumableData.categoryId) updateData.category_id = consumableData.categoryId;
      if (consumableData.unit) updateData.unit = consumableData.unit;
      if (consumableData.minQty !== undefined) updateData.min_qty = consumableData.minQty;
      if (consumableData.maxQty !== undefined) updateData.max_qty = consumableData.maxQty;
      if (consumableData.unitCost !== undefined) updateData.unit_cost = consumableData.unitCost;
      if (consumableData.vendor !== undefined) updateData.vendor = consumableData.vendor;
      if (consumableData.status !== undefined) updateData.status = consumableData.status;
      if (consumableData.isCritical !== undefined) updateData.is_critical = consumableData.isCritical;
      if (consumableData.leadTimeDays !== undefined) updateData.lead_time_days = consumableData.leadTimeDays;
      if (consumableData.properties !== undefined) updateData.properties = consumableData.properties;

      const response = await apiCall(`/masters/consumables/${id}/`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });
      if (response.success) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to update consumable');
    } catch (error) {
      console.error('Error updating consumable:', error);
      throw error;
    }
  }

  // Delete consumable
  async deleteConsumable(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/masters/consumables/${id}/`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting consumable:', error);
      throw error;
    }
  }

  // Get stock for consumable
  async getConsumableStock(id: string): Promise<any> {
    try {
      const response = await apiCall(`/masters/consumables/${id}/stock/`);
      if (response.success) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch stock');
    } catch (error) {
      console.error('Error fetching stock:', error);
      throw error;
    }
  }

  // Prepare data for export
  prepareConsumablesForExport(consumables: MastersConsumable[]) {
    return consumables.map(consumable => ({
      'Item No': consumable.itemNo,
      'Name': consumable.name,
      'Category': consumable.category?.name || 'Unknown',
      'Unit': consumable.unit,
      'Min Qty': consumable.minQty,
      'Max Qty': consumable.maxQty,
      'Unit Cost': `$${consumable.unitCost}`,
      'Available Stock': consumable.availableStock || 0,
      'Vendor': consumable.vendor || 'N/A',
      'Status': consumable.status ? 'Active' : 'Inactive',
      'Critical': consumable.isCritical ? 'Yes' : 'No',
      'Lead Time (Days)': consumable.leadTimeDays
    }));
  }
}

const mastersConsumablesService = new MastersConsumablesService();
export default mastersConsumablesService; 