#!/usr/bin/env python3
"""
Test script for L&A checklist functionality.
This script tests the L&A checklist API endpoints.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime

# API Configuration
API_BASE_URL = "http://localhost:8000/api"
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def get_auth_token():
    """Get authentication token."""
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login",
            json=TEST_USER,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_la_checklist_endpoints():
    """Test L&A checklist endpoints."""
    
    print("🔧 Testing L&A Checklist API...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test 1: Get available assets
        print("\n📋 Test 1: Get available assets for L&A checklist")
        response = requests.get(
            f"{API_BASE_URL}/la-checklist/available-assets",
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            assets = response.json()
            print(f"✅ Found {len(assets)} available assets")
            for asset in assets[:3]:  # Show first 3
                print(f"   - {asset.get('asset_id', 'N/A')}: {asset.get('type', 'N/A')} ({asset.get('status', 'N/A')})")
        else:
            print(f"❌ Failed: {response.text}")
            
        # Test 2: Get existing L&A sessions
        print("\n📋 Test 2: Get L&A checklist sessions")
        response = requests.get(
            f"{API_BASE_URL}/la-checklist/sessions",
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            sessions = response.json()
            print(f"✅ Found {len(sessions)} L&A sessions")
            for session in sessions[:3]:  # Show first 3
                print(f"   - {session.get('session_id', 'N/A')}: Asset {session.get('asset_id', 'N/A')} ({session.get('status', 'N/A')})")
        else:
            print(f"❌ Failed: {response.text}")
            
        # Test 3: Test asset creation (if no assets available)
        if response.status_code == 200:
            assets = requests.get(f"{API_BASE_URL}/la-checklist/available-assets", headers=headers).json()
            if len(assets) == 0:
                print("\n📋 Test 3: Creating sample asset for testing")
                
                # Create a simple asset directly in database
                from app.config.database import get_db
                from app.models.assets import Asset, AssetStatus, AssetCondition
                
                db = next(get_db())
                try:
                    test_asset = Asset(
                        asset_id="TEST-EVM-001",
                        type="Electronic Voting Machine",
                        model="Test Model",
                        status=AssetStatus.NEW,
                        condition=AssetCondition.GOOD,
                        location="Test Warehouse",
                        state="Georgia",
                        county="Fulton",
                        precinct="Test Precinct"
                    )
                    db.add(test_asset)
                    db.commit()
                    print("✅ Created test asset: TEST-EVM-001")
                    
                    # Test starting L&A session
                    print("\n📋 Test 4: Start L&A checklist session")
                    session_data = {
                        "asset_id": test_asset.id,
                        "notes": "Test L&A session for integration testing"
                    }
                    
                    response = requests.post(
                        f"{API_BASE_URL}/la-checklist/sessions",
                        json=session_data,
                        headers=headers
                    )
                    
                    print(f"Status: {response.status_code}")
                    if response.status_code == 200:
                        session = response.json()
                        print(f"✅ Created L&A session: {session.get('session_id', 'N/A')}")
                        print(f"   Asset: {session.get('asset_id', 'N/A')}")
                        print(f"   Status: {session.get('status', 'N/A')}")
                        print(f"   Items: {len(session.get('checklist_items', []))}")
                        
                        return True
                    else:
                        print(f"❌ Failed to create session: {response.text}")
                        
                except Exception as e:
                    print(f"❌ Error creating test asset: {e}")
                    db.rollback()
                finally:
                    db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_asset_endpoints():
    """Test basic asset endpoints."""
    
    print("\n🔧 Testing Asset API...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test: Get assets
        print("\n📋 Test: Get all assets")
        response = requests.get(
            f"{API_BASE_URL}/assets",
            headers=headers
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            assets = data.get('data', [])
            print(f"✅ Found {len(assets)} assets")
            for asset in assets[:3]:  # Show first 3
                print(f"   - {asset.get('asset_id', 'N/A')}: {asset.get('type', 'N/A')} ({asset.get('status', 'N/A')})")
        else:
            print(f"❌ Failed: {response.text}")
            
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting L&A Checklist API Tests...")
    
    # Test basic asset endpoints first
    asset_success = test_asset_endpoints()
    
    # Test L&A checklist endpoints
    la_success = test_la_checklist_endpoints()
    
    if asset_success and la_success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed. Check output above.")
        sys.exit(1)
