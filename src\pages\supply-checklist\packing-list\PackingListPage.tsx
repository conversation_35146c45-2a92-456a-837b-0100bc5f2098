import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowLeft, Plus, Search, Filter, Download, Printer, Eye, Edit, Trash, AlertTriangle } from "lucide-react";
import { exportData } from "@/utils/exportUtils";
import { ExportOptions } from "@/components/ExportOptions";
import { PackingListAddEditDialog } from "@/components/packing-list/PackingListAddEditDialog";
import { PackingListViewDialog } from "@/components/packing-list/PackingListViewDialog";
import { workflowService } from "@/services/workflowService";

// PackingList interface
interface PackingList {
  id: string;
  refNo: string;
  description: string;
  status: string;
  lastUpdatedBy?: string;
  lastUpdated: string;
  items?: any[];
}

const PackingListPage = () => {
  const navigate = useNavigate();
  const [packingLists, setPackingLists] = useState<PackingList[]>([]);
  const [filteredPackingLists, setFilteredPackingLists] = useState<PackingList[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPackingList, setSelectedPackingList] = useState<PackingList | undefined>(undefined);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Backend data states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load packing lists from backend
  useEffect(() => {
    const loadPackingLists = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await workflowService.getPackingLists();

        if (response.success && response.data) {
          setPackingLists(response.data);
          setFilteredPackingLists(response.data);
        } else {
          setError('Failed to load packing lists');
          setPackingLists([]);
          setFilteredPackingLists([]);
        }
      } catch (err) {
        console.error('Error loading packing lists:', err);
        setError('Failed to load packing lists');
        setPackingLists([]);
        setFilteredPackingLists([]);
      } finally {
        setLoading(false);
      }
    };

    loadPackingLists();
  }, []);

  // Filter packing lists when search query changes
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredPackingLists(packingLists);
    } else {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = packingLists.filter(
        (packingList) =>
          packingList.refNo.toLowerCase().includes(lowercaseQuery) ||
          packingList.description.toLowerCase().includes(lowercaseQuery) ||
          (packingList.lastUpdatedBy && packingList.lastUpdatedBy.toLowerCase().includes(lowercaseQuery))
      );
      setFilteredPackingLists(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, packingLists]);

  // Handle adding a new packing list
  const handleAddPackingList = () => {
    setSelectedPackingList(undefined);
    setShowAddDialog(true);
  };

  // Handle editing a packing list
  const handleEditPackingList = (packingList: PackingList) => {
    setSelectedPackingList(packingList);
    setShowEditDialog(true);
  };

  // Handle viewing a packing list
  const handleViewPackingList = (packingList: PackingList) => {
    setSelectedPackingList(packingList);
    setShowViewDialog(true);
  };

  // Handle navigating to detail page
  const handleNavigateToDetail = (packingList: PackingList) => {
    navigate(`/supply-checklist/packing-list/${packingList.id}`);
  };

  // Handle deleting a packing list
  const handleDeletePackingList = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this packing list?")) {
      try {
        const response = await workflowService.deletePackingList(id);
        if (response.success) {
          setPackingLists(packingLists.filter(pl => pl.id !== id));
          setFilteredPackingLists(filteredPackingLists.filter(pl => pl.id !== id));
        } else {
          alert('Failed to delete packing list');
        }
      } catch (err) {
        console.error('Error deleting packing list:', err);
        alert('Failed to delete packing list');
      }
    }
  };

  // Handle export
  const handleExport = (format: string) => {
    // Prepare data for export
    const exportableData = filteredPackingLists.map(pl => ({
      'Ref No': pl.refNo,
      'Description': pl.description,
      'Status': pl.status,
      'Last Updated By': pl.lastUpdatedBy || 'N/A',
      'Last Updated Date': pl.lastUpdated,
      'Items Count': pl.items?.length || 0
    }));

    exportData(
      exportableData,
      format,
      'Packing_Lists_Export',
      'Packing Lists',
      ['Ref No', 'Description', 'Status', 'Last Updated By', 'Last Updated Date', 'Items Count']
    );
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredPackingLists.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredPackingLists.length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Handle packing list update
  const handlePackingListUpdated = (updatedList: PackingList) => {
    setPackingLists(prevLists => {
      const newLists = [...prevLists];
      const index = newLists.findIndex(pl => pl.id === updatedList.id);
      if (index !== -1) {
        newLists[index] = updatedList;
      } else {
        newLists.push(updatedList);
      }
      return newLists;
    });
  };

  return (
    <AppLayout>
      <div className="p-8">
        <div className="flex justify-between items-center mb-6">
          <div>
            <div className="flex items-center mb-2">
              <Link to="/supply-checklist/election-readiness">
                <Button variant="ghost" size="sm" className="mr-2">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Supply Checklist
                </Button>
              </Link>
            </div>
            <h1 className="text-2xl font-bold mb-2">Packing List</h1>
            <p className="text-muted-foreground">
              Manage and track packing lists for election equipment
            </p>
          </div>
          <div className="flex gap-2">
            <ExportOptions onExport={handleExport} className="h-9" />
            <Button size="sm" onClick={handleAddPackingList}>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        <div className="mb-6 flex justify-between items-center">
          <div className="flex gap-4 w-1/2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <Card>
            <CardContent className="p-8">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-muted-foreground">Loading packing lists...</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {error && (
          <Card>
            <CardContent className="p-8">
              <div className="text-center text-red-600">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Packing Lists Table - Only show when not loading and no error */}
        {!loading && !error && (
          <Card>
            <CardContent className="p-0">
              <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12"></TableHead>
                  <TableHead>Ref No</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Last Updated By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.map((packingList) => (
                  <TableRow
                    key={packingList.id}
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleNavigateToDetail(packingList)}
                  >
                    <TableCell onClick={(e) => e.stopPropagation()}>
                      {packingList.image ? (
                        <img
                          src={packingList.image}
                          alt={packingList.description}
                          className="w-10 h-10 object-contain"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gray-200 flex items-center justify-center">
                          <span className="text-xs text-gray-500">No img</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{packingList.refNo}</TableCell>
                    <TableCell>{packingList.description}</TableCell>
                    <TableCell>{packingList.lastUpdatedBy || 'N/A'}</TableCell>
                    <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                      <div className="flex justify-end gap-2">
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          handleViewPackingList(packingList);
                        }}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          handleEditPackingList(packingList);
                        }}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePackingList(packingList.id);
                        }}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {currentItems.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                      No packing lists found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
        )}

        {/* Pagination - Only show when not loading and no error */}
        {!loading && !error && totalPages > 1 && (
          <div className="flex justify-between items-center mt-4">
            <div>
              Show <span className="font-medium">{itemsPerPage}</span> entries
            </div>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button variant="outline" size="sm" className="px-4">
                {currentPage}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Add Dialog */}
        <PackingListAddEditDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          packingList={undefined}
          onSave={handlePackingListUpdated}
          isEditMode={false}
        />

        {/* Edit Dialog */}
        <PackingListAddEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          packingList={selectedPackingList}
          onSave={handlePackingListUpdated}
          isEditMode={true}
        />

        {/* View Dialog */}
        <PackingListViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          packingList={selectedPackingList}
        />
      </div>
    </AppLayout>
  );
};

export default PackingListPage;
