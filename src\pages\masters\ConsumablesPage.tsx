import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Plus, FileDown, Edit, Trash, AlertTriangle } from "lucide-react";
import consumablescategoryService, { ConsumableCategory, ConsumableCategoryCreationData } from "@/services/consumablescategoryService";
import { exportData } from "@/utils/exportUtils";
import { ExportOptions } from "@/components/ExportOptions";
import mastersConsumablesService, { MastersConsumable, MastersConsumableCreationData } from "@/services/mastersConsumablesService";

// Simple Add/Edit Dialog Component
function CategoryDialog({ 
  open, 
  onOpenChange, 
  category, 
  onSave 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  category?: ConsumableCategory; 
  onSave: (data: ConsumableCategoryCreationData) => void; 
}) {
  const [formData, setFormData] = useState<ConsumableCategoryCreationData>({
    name: '',
    status: true
  });

  useEffect(() => {
    if (category && open) {
      setFormData({
        name: category.name,
        status: category.status
      });
    } else if (open && !category) {
      setFormData({
        name: '',
        status: true
      });
    }
  }, [category, open]);

  const handleSave = () => {
    onSave(formData);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {category ? 'Edit Category' : 'Add New Category'}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name *
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="col-span-3"
              placeholder="Enter category name"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Active
            </Label>
            <div className="col-span-3">
              <Switch
                id="status"
                checked={formData.status}
                onCheckedChange={(checked) => setFormData({ ...formData, status: checked })}
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={!formData.name.trim()}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Simple View Dialog Component
function CategoryViewDialog({ 
  open, 
  onOpenChange, 
  category 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  category?: ConsumableCategory; 
}) {
  if (!category) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            Category Details
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">
              Name:
            </Label>
            <div className="col-span-3">
              {category.name}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">
              Status:
            </Label>
            <div className="col-span-3">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                category.status ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {category.status ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">
              Created:
            </Label>
            <div className="col-span-3">
              {new Date(category.createdAt).toLocaleDateString()}
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">
              Updated:
            </Label>
            <div className="col-span-3">
              {new Date(category.updatedAt).toLocaleDateString()}
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Add Dialog Component for creating/editing consumables
function ConsumableDialog({ 
  open, 
  onOpenChange, 
  consumable, 
  categories,
  onSave 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  consumable?: MastersConsumable;
  categories: ConsumableCategory[];
  onSave: (data: MastersConsumableCreationData) => void; 
}) {
  console.log('Categories in dialog:', categories); // Debug log
  
  const [formData, setFormData] = useState({
    itemNo: '',
    name: '',
    description: '',
    categoryId: '',
    unit: 'pieces',
    minQty: 0,
    maxQty: 1000,
    unitCost: 0,
    vendor: '',
    status: true,
    isCritical: false,
    leadTimeDays: 7
  });

  useEffect(() => {
    if (consumable && open) {
      setFormData({
        itemNo: consumable.itemNo,
        name: consumable.name,
        description: consumable.description || '',
        categoryId: consumable.categoryId,
        unit: consumable.unit,
        minQty: consumable.minQty,
        maxQty: consumable.maxQty,
        unitCost: consumable.unitCost,
        vendor: consumable.vendor || '',
        status: consumable.status,
        isCritical: consumable.isCritical,
        leadTimeDays: consumable.leadTimeDays
      });
    } else if (!consumable && open) {
      setFormData({
        itemNo: '',
        name: '',
        description: '',
        categoryId: '',
        unit: 'pieces',
        minQty: 0,
        maxQty: 1000,
        unitCost: 0,
        vendor: '',
        status: true,
        isCritical: false,
        leadTimeDays: 7
      });
    }
  }, [consumable, open]);

  const handleSave = () => {
    if (formData.itemNo && formData.name && formData.categoryId) {
      onSave(formData);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{consumable ? 'Edit' : 'Add New'} Consumable</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="itemNo" className="text-right">Reference Number *</Label>
            <Input
              id="itemNo"
              className="col-span-3"
              value={formData.itemNo}
              onChange={(e) => setFormData({...formData, itemNo: e.target.value})}
              placeholder="Enter item number"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">Consumable Name *</Label>
            <Input
              id="name"
              className="col-span-3"
              value={formData.name}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              placeholder="Enter consumable name"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">Category *</Label>
            <Select value={formData.categoryId} onValueChange={(value) => setFormData({...formData, categoryId: value})}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.length > 0 ? (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="" disabled>No categories available</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="unit" className="text-right">Unit</Label>
            <Select value={formData.unit} onValueChange={(value) => setFormData({...formData, unit: value})}>
              <SelectTrigger className="col-span-3">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pieces">Pieces</SelectItem>
                <SelectItem value="boxes">Boxes</SelectItem>
                <SelectItem value="units">Units</SelectItem>
                <SelectItem value="packs">Packs</SelectItem>
                <SelectItem value="rolls">Rolls</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="minQty" className="text-right">Min QTY</Label>
            <Input
              id="minQty"
              type="number"
              className="col-span-3"
              value={formData.minQty}
              onChange={(e) => setFormData({...formData, minQty: parseInt(e.target.value) || 0})}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="maxQty" className="text-right">Max QTY</Label>
            <Input
              id="maxQty"
              type="number"
              className="col-span-3"
              value={formData.maxQty}
              onChange={(e) => setFormData({...formData, maxQty: parseInt(e.target.value) || 1000})}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="unitCost" className="text-right">Unit Cost</Label>
            <Input
              id="unitCost"
              type="number"
              step="0.01"
              className="col-span-3"
              value={formData.unitCost}
              onChange={(e) => setFormData({...formData, unitCost: parseFloat(e.target.value) || 0})}
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="vendor" className="text-right">Vendor</Label>
            <Input
              id="vendor"
              className="col-span-3"
              value={formData.vendor}
              onChange={(e) => setFormData({...formData, vendor: e.target.value})}
              placeholder="Enter vendor name"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">Description</Label>
            <Input
              id="description"
              className="col-span-3"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Enter description"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="leadTime" className="text-right">Lead Time (Days)</Label>
            <Input
              id="leadTime"
              type="number"
              className="col-span-3"
              value={formData.leadTimeDays}
              onChange={(e) => setFormData({...formData, leadTimeDays: parseInt(e.target.value) || 7})}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSave} disabled={!formData.itemNo || !formData.name || !formData.categoryId}>
            {consumable ? 'Update' : 'Add'} Consumable
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// View Dialog Component
function ConsumableViewDialog({ 
  open, 
  onOpenChange, 
  consumable 
}: { 
  open: boolean; 
  onOpenChange: (open: boolean) => void; 
  consumable?: MastersConsumable; 
}) {
  if (!consumable) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Consumable Details</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Reference Number:</Label>
            <div className="col-span-3">{consumable.itemNo}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Name:</Label>
            <div className="col-span-3">{consumable.name}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Category:</Label>
            <div className="col-span-3">{consumable.category?.name || 'Unknown'}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Unit:</Label>
            <div className="col-span-3">{consumable.unit}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Min Qty:</Label>
            <div className="col-span-3">{consumable.minQty}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Available Stock:</Label>
            <div className="col-span-3">{consumable.availableStock || 0}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Unit Cost:</Label>
            <div className="col-span-3">${consumable.unitCost}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Vendor:</Label>
            <div className="col-span-3">{consumable.vendor || 'N/A'}</div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Status:</Label>
            <div className="col-span-3">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                consumable.status ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {consumable.status ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label className="text-right font-medium">Critical:</Label>
            <div className="col-span-3">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                consumable.isCritical ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {consumable.isCritical ? 'Yes' : 'No'}
              </span>
            </div>
          </div>
          {consumable.description && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-medium">Description:</Label>
              <div className="col-span-3">{consumable.description}</div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default function ConsumablesPage() {
  const [consumables, setConsumables] = useState<MastersConsumable[]>([]);
  const [filteredConsumables, setFilteredConsumables] = useState<MastersConsumable[]>([]);
  const [categories, setCategories] = useState<ConsumableCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedConsumable, setSelectedConsumable] = useState<MastersConsumable | undefined>(undefined);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data from backend
  useEffect(() => {
    loadData();
  }, []);

  // Filter consumables when search query changes
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredConsumables(consumables || []);
    } else {
      const lowercaseQuery = searchQuery.toLowerCase();
      const filtered = (consumables || []).filter(
        (consumable) =>
          consumable?.name?.toLowerCase().includes(lowercaseQuery) ||
          consumable?.itemNo?.toLowerCase().includes(lowercaseQuery) ||
          consumable?.category?.name?.toLowerCase().includes(lowercaseQuery)
      );
      setFilteredConsumables(filtered);
    }
    setCurrentPage(1);
  }, [searchQuery, consumables]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load both consumables and categories
      console.log('Starting to load data...'); // Debug log
      
      let consumablesData = [];
      let categoriesData = [];
      
      try {
        console.log('Loading categories...'); // Debug log
        const categoriesResult = await consumablescategoryService.getAllConsumableCategories();
        console.log('Categories result:', categoriesResult); // Debug log
        categoriesData = categoriesResult?.consumableCategories || [];
        console.log('Categories data:', categoriesData); // Debug log
      } catch (categoriesError) {
        console.error('Failed to load categories:', categoriesError);
        setError(`Failed to load categories: ${categoriesError.message}`);
      }
      
      try {
        console.log('Loading consumables...'); // Debug log
        const consumablesResult = await mastersConsumablesService.getAllConsumables({ limit: 100 });
        console.log('Consumables result:', consumablesResult); // Debug log
        consumablesData = consumablesResult?.consumables || [];
      } catch (consumablesError) {
        console.error('Failed to load consumables:', consumablesError);
        // Don't fail the whole page if consumables fail, categories are more important for "Add New"
      }
      
      setConsumables(consumablesData);
      setFilteredConsumables(consumablesData);
      setCategories(categoriesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      console.error('Error loading data:', err);
      // Set empty arrays on error to prevent undefined issues
      setConsumables([]);
      setFilteredConsumables([]);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle adding a new consumable
  const handleAddConsumable = async (consumableData: MastersConsumableCreationData) => {
    try {
      const newConsumable = await mastersConsumablesService.createConsumable(consumableData);
      setConsumables([...(consumables || []), newConsumable]);
      setShowAddDialog(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add consumable');
      console.error('Error adding consumable:', err);
    }
  };

  // Handle editing a consumable
  const handleEditConsumable = async (consumableData: MastersConsumableCreationData) => {
    if (selectedConsumable) {
      try {
        const updatedConsumable = await mastersConsumablesService.updateConsumable(
          selectedConsumable.id, 
          consumableData
        );

        setConsumables((consumables || []).map(c =>
          c.id === updatedConsumable.id ? updatedConsumable : c
        ));
        setShowEditDialog(false);
        setSelectedConsumable(undefined);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update consumable');
        console.error('Error updating consumable:', err);
      }
    }
  };

  // Handle deleting a consumable
  const handleDeleteConsumable = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this consumable?")) {
      try {
        const success = await mastersConsumablesService.deleteConsumable(id);
        if (success) {
          setConsumables((consumables || []).filter(c => c.id !== id));
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete consumable');
        console.error('Error deleting consumable:', err);
      }
    }
  };

  // Handle opening the view dialog
  const handleViewConsumable = (consumable: MastersConsumable) => {
    setSelectedConsumable(consumable);
    setShowViewDialog(true);
  };

  // Handle opening the edit dialog
  const handleEditClick = (consumable: MastersConsumable) => {
    setSelectedConsumable(consumable);
    setShowEditDialog(true);
  };

  // Handle export
  const handleExport = (format: string) => {
    const exportableData = mastersConsumablesService.prepareConsumablesForExport(filteredConsumables || []);

    exportData(
      exportableData,
      format,
      'Consumables_Export',
      'Consumables List',
      ['Item No', 'Name', 'Category', 'Unit', 'Min Qty', 'Available Stock', 'Unit Cost', 'Vendor', 'Status']
    );
  };

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = (filteredConsumables || []).slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil((filteredConsumables || []).length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <p>Loading consumables...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600">{error}</p>
              <Button onClick={loadData} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Consumables</h1>
            <p className="text-sm text-gray-500 mt-1">
              {categories.length > 0 
                ? `${categories.length} categories available` 
                : 'Loading categories...'}
              {categories.length === 0 && !loading && (
                <Button 
                  variant="link" 
                  size="sm" 
                  onClick={loadData}
                  className="p-0 h-auto ml-2 text-blue-600"
                >
                  Retry Loading Categories
                </Button>
              )}
            </p>
          </div>
          <div className="flex gap-2">
            <ExportOptions onExport={handleExport} />
            <Button 
              onClick={() => setShowAddDialog(true)}
              disabled={loading || categories.length === 0}
              title={categories.length === 0 ? 'Categories must be loaded first' : ''}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        <div className="flex items-center mb-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search consumables..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Item No.</TableHead>
                  <TableHead>Min Qty</TableHead>
                  <TableHead>Available Stock</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentItems.map((consumable) => (
                  <TableRow key={consumable.id} className="cursor-pointer hover:bg-gray-50" onClick={() => handleViewConsumable(consumable)}>
                    <TableCell className="font-medium">{consumable.name}</TableCell>
                    <TableCell>{consumable.category?.name || 'Unknown'}</TableCell>
                    <TableCell>{consumable.itemNo}</TableCell>
                    <TableCell>{consumable.minQty}</TableCell>
                    <TableCell>
                      <span className={`font-medium ${
                        (consumable.availableStock || 0) <= consumable.minQty ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {consumable.availableStock || 0}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2" onClick={(e) => e.stopPropagation()}>
                        <Button variant="ghost" size="sm" onClick={() => handleEditClick(consumable)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDeleteConsumable(consumable.id)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {currentItems.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                      No consumables found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            {Array.from({ length: totalPages }, (_, i) => (
              <Button
                key={i + 1}
                variant={currentPage === i + 1 ? "default" : "outline"}
                onClick={() => paginate(i + 1)}
              >
                {i + 1}
              </Button>
            ))}
            <Button
              variant="outline"
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* Add Dialog */}
        <ConsumableDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
          categories={categories}
          onSave={handleAddConsumable}
        />

        {/* Edit Dialog */}
        <ConsumableDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          consumable={selectedConsumable}
          categories={categories}
          onSave={handleEditConsumable}
        />

        {/* View Dialog */}
        <ConsumableViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          consumable={selectedConsumable}
        />
      </div>
    </AppLayout>
  );
}
