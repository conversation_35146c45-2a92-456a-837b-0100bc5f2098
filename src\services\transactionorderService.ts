// Transaction Order Service - Frontend API service for transaction order management
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface TransactionOrderItem {
  item: string;
  consumable: string;
  qty: number;
  amount: number;
  unitPrice?: number;
}

export interface TransactionOrder {
  id?: number;
  refNo?: number;
  vendor: string;
  date: string | Date;
  Location: string;
  consumable: string;
  qty: number; // Added qty field at order level
  items: TransactionOrderItem[];
  total: number;
  status: 'pending' | 'approved' | 'completed' | 'cancelled';
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TransactionOrderFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  vendor?: string;
  location?: string; // Changed from stockLocation to location
  consumable?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface TransactionOrderSummary {
  totalOrders: number;
  pendingOrders: number;
  inTransitOrders: number;
  ordersThisMonth: number;
}

export interface ConsumableSummary {
  [consumable: string]: {
    totalOrders: number;
    totalAmount: number;
    totalQty: number; // Added totalQty for consumable level
    totalItems: number;
    locations: string[];
    items: {
      [item: string]: {
        totalQty: number;
        totalAmount: number;
      };
    };
  };
}

export interface StockSummary {
  [locationConsumable: string]: {
    orderQty: number; // Added order-level qty tracking
    items: {
      [itemConsumable: string]: {
        item: string;
        consumable: string;
        location: string;
        orderConsumable: string;
        totalQty: number;
        totalAmount: number;
      };
    };
  };
}

export interface ConsumableCategory {
  id: number;
  name: string;
}

export interface PackingLocation {
  id: number;
  name: string;
}

export interface VendorOption {
  name: string;
}

export interface ConsumableOption {
  name: string;
}

export interface PaginatedResponse<T> {
  success?: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiResponse<T> {
  success?: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface TransactionOrderStats {
  totalOrders: number;
  totalAmount: number;
  totalQty: number; // Added total quantity stats
  pendingAmount: number;
  completedAmount: number;
  pendingQty: number; // Added pending quantity stats
  completedQty: number; // Added completed quantity stats
  vendorBreakdown: Array<{ vendor: string; count: number; amount: number; qty: number }>;
  consumableBreakdown: Array<{ consumable: string; count: number; amount: number; qty: number }>;
  locationBreakdown: Array<{ location: string; count: number; amount: number; qty: number }>;
  statusBreakdown: Array<{ status: string; count: number; qty: number }>;
  monthlyTrend: Array<{ month: string; orders: number; amount: number; qty: number }>;
}

export interface BulkDeleteResponse {
  success: boolean;
  message: string;
  deletedCount: number;
}

class TransactionOrderService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/transaction-orders${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if token exists
        ...(localStorage.getItem('authToken') && {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        })
      }
    };

    const response = await fetch(url, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Get all transaction orders with optional filtering
  async getTransactionOrders(filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = queryParams.toString() ? `?${queryParams.toString()}` : '';
    return this.makeRequest<PaginatedResponse<TransactionOrder>>(endpoint);
  }

  // Get transaction order summary statistics
  async getOrderSummary(): Promise<TransactionOrderSummary> {
    const response = await this.makeRequest<ApiResponse<TransactionOrderSummary>>('/summary');
    return response.data!;
  }

  // Get consumable summary statistics
  async getConsumableSummary(filters: {
    consumable?: string;
    dateFrom?: string;
    dateTo?: string;
  } = {}): Promise<ConsumableSummary> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/consumable-summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.makeRequest<ApiResponse<ConsumableSummary>>(endpoint);
    return response.data!;
  }

  // Get stock summary by location and item
  async getStockSummary(filters: {
    location?: string;
    item?: string;
    consumable?: string;
  } = {}): Promise<StockSummary> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/stock-summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await this.makeRequest<ApiResponse<StockSummary>>(endpoint);
    return response.data!;
  }

  // Get specific transaction order by ID
  async getTransactionOrderById(id: number): Promise<TransactionOrder> {
    const response = await this.makeRequest<ApiResponse<TransactionOrder>>(`/${id}`);
    return response.data!;
  }

  // Create new transaction order
  async createTransactionOrder(orderData: Partial<TransactionOrder>): Promise<TransactionOrder> {
    const response = await this.makeRequest<ApiResponse<TransactionOrder>>('', {
      method: 'POST',
      body: JSON.stringify(orderData)
    });
    return response.data!;
  }

  // Update transaction order
  async updateTransactionOrder(id: number, orderData: Partial<TransactionOrder>): Promise<TransactionOrder> {
    const response = await this.makeRequest<ApiResponse<TransactionOrder>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(orderData)
    });
    return response.data!;
  }

  // Update order status
  async updateOrderStatus(id: number, status: TransactionOrder['status']): Promise<TransactionOrder> {
    const response = await this.makeRequest<ApiResponse<TransactionOrder>>(`/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status })
    });
    return response.data!;
  }

  // Delete transaction order
  async deleteTransactionOrder(id: number): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>(`/${id}`, {
      method: 'DELETE'
    });
  }

  // Bulk delete transaction orders
  async bulkDeleteTransactionOrders(ids: number[]): Promise<BulkDeleteResponse> {
    return this.makeRequest<BulkDeleteResponse>('', {
      method: 'DELETE',
      body: JSON.stringify({ ids })
    });
  }

  // Configuration endpoints
  
  // Get consumable categories
  async getConsumableCategories(): Promise<ConsumableCategory[]> {
    const response = await this.makeRequest<ApiResponse<ConsumableCategory[]>>('/config/consumable-categories');
    return response.data!;
  }

  // Get packing locations
  async getPackingLocations(): Promise<PackingLocation[]> {
    const response = await this.makeRequest<ApiResponse<PackingLocation[]>>('/config/packing-locations');
    return response.data!;
  }

  // Get distinct vendors
  async getVendors(): Promise<VendorOption[]> {
    const response = await this.makeRequest<ApiResponse<VendorOption[]>>('/config/vendors');
    return response.data!;
  }

  // Get distinct consumables
  async getConsumables(): Promise<ConsumableOption[]> {
    const response = await this.makeRequest<ApiResponse<ConsumableOption[]>>('/config/consumables');
    return response.data!;
  }

  // Search transaction orders
  async searchTransactionOrders(query: string, filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    const searchFilters = { ...filters, search: query };
    return this.getTransactionOrders(searchFilters);
  }

  // Get orders by vendor
  async getOrdersByVendor(vendor: string, filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getTransactionOrders({ ...filters, vendor });
  }

  // Get orders by location
  async getOrdersByLocation(location: string, filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getTransactionOrders({ ...filters, location });
  }

  // Get orders by consumable
  async getOrdersByConsumable(consumable: string, filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getTransactionOrders({ ...filters, consumable });
  }

  // Get orders by status
  async getOrdersByStatus(status: TransactionOrder['status'], filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getTransactionOrders({ ...filters, status });
  }

  // Get orders by date range
  async getOrdersByDateRange(
    dateFrom: string | Date,
    dateTo: string | Date,
    filters: TransactionOrderFilters = {}
  ): Promise<PaginatedResponse<TransactionOrder>> {
    const dateFromStr = typeof dateFrom === 'string' ? dateFrom : dateFrom.toISOString().split('T')[0];
    const dateToStr = typeof dateTo === 'string' ? dateTo : dateTo.toISOString().split('T')[0];
    
    return this.getTransactionOrders({
      ...filters,
      dateFrom: dateFromStr,
      dateTo: dateToStr
    });
  }

  // Get pending orders
  async getPendingOrders(filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getOrdersByStatus('pending', filters);
  }

  // Get approved orders
  async getApprovedOrders(filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getOrdersByStatus('approved', filters);
  }

  // Get completed orders
  async getCompletedOrders(filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getOrdersByStatus('completed', filters);
  }

  // Get cancelled orders
  async getCancelledOrders(filters: TransactionOrderFilters = {}): Promise<PaginatedResponse<TransactionOrder>> {
    return this.getOrdersByStatus('cancelled', filters);
  }

  // Get recent orders (last 10)
  async getRecentOrders(): Promise<TransactionOrder[]> {
    const response = await this.getTransactionOrders({ 
      limit: 10, 
      page: 1,
      sortBy: 'createdAt',
      sortOrder: 'DESC'
    });
    return response.data;
  }

  // Get orders this month
  async getOrdersThisMonth(): Promise<PaginatedResponse<TransactionOrder>> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    return this.getOrdersByDateRange(startOfMonth, endOfMonth);
  }

  // Approve order
  async approveOrder(id: number): Promise<TransactionOrder> {
    return this.updateOrderStatus(id, 'approved');
  }

  // Complete order
  async completeOrder(id: number): Promise<TransactionOrder> {
    return this.updateOrderStatus(id, 'completed');
  }

  // Cancel order
  async cancelOrder(id: number): Promise<TransactionOrder> {
    return this.updateOrderStatus(id, 'cancelled');
  }

  // Bulk approve orders
  async bulkApproveOrders(ids: number[]): Promise<TransactionOrder[]> {
    const promises = ids.map(id => this.approveOrder(id));
    return Promise.all(promises);
  }

  // Bulk complete orders
  async bulkCompleteOrders(ids: number[]): Promise<TransactionOrder[]> {
    const promises = ids.map(id => this.completeOrder(id));
    return Promise.all(promises);
  }

  // Get comprehensive statistics
  async getComprehensiveStats(filters: {
    dateFrom?: string;
    dateTo?: string;
    vendor?: string;
    location?: string;
    consumable?: string;
  } = {}): Promise<TransactionOrderStats> {
    try {
      const [orders, summary] = await Promise.all([
        this.getTransactionOrders({ ...filters, limit: 1000 }),
        this.getOrderSummary()
      ]);

      const allOrders = orders.data;
      const totalAmount = allOrders.reduce((sum, order) => sum + (order.total || 0), 0);
      const totalQty = allOrders.reduce((sum, order) => sum + (order.qty || 0), 0);
      
      const pendingAmount = allOrders
        .filter(order => order.status === 'pending')
        .reduce((sum, order) => sum + (order.total || 0), 0);
      const pendingQty = allOrders
        .filter(order => order.status === 'pending')
        .reduce((sum, order) => sum + (order.qty || 0), 0);
        
      const completedAmount = allOrders
        .filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + (order.total || 0), 0);
      const completedQty = allOrders
        .filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + (order.qty || 0), 0);

      // Vendor breakdown
      const vendorMap = new Map<string, { count: number; amount: number; qty: number }>();
      allOrders.forEach(order => {
        const vendor = order.vendor;
        if (!vendorMap.has(vendor)) {
          vendorMap.set(vendor, { count: 0, amount: 0, qty: 0 });
        }
        const vendorData = vendorMap.get(vendor)!;
        vendorData.count += 1;
        vendorData.amount += order.total || 0;
        vendorData.qty += order.qty || 0;
      });

      const vendorBreakdown = Array.from(vendorMap.entries()).map(([vendor, data]) => ({
        vendor,
        count: data.count,
        amount: data.amount,
        qty: data.qty
      }));

      // Consumable breakdown
      const consumableMap = new Map<string, { count: number; amount: number; qty: number }>();
      allOrders.forEach(order => {
        const consumable = order.consumable;
        if (!consumableMap.has(consumable)) {
          consumableMap.set(consumable, { count: 0, amount: 0, qty: 0 });
        }
        const consumableData = consumableMap.get(consumable)!;
        consumableData.count += 1;
        consumableData.amount += order.total || 0;
        consumableData.qty += order.qty || 0;
      });

      const consumableBreakdown = Array.from(consumableMap.entries()).map(([consumable, data]) => ({
        consumable,
        count: data.count,
        amount: data.amount,
        qty: data.qty
      }));

      // Location breakdown
      const locationMap = new Map<string, { count: number; amount: number; qty: number }>();
      allOrders.forEach(order => {
        const location = order.Location;
        if (!locationMap.has(location)) {
          locationMap.set(location, { count: 0, amount: 0, qty: 0 });
        }
        const locationData = locationMap.get(location)!;
        locationData.count += 1;
        locationData.amount += order.total || 0;
        locationData.qty += order.qty || 0;
      });

      const locationBreakdown = Array.from(locationMap.entries()).map(([location, data]) => ({
        location,
        count: data.count,
        amount: data.amount,
        qty: data.qty
      }));

      // Status breakdown
      const statusMap = new Map<string, { count: number; qty: number }>();
      allOrders.forEach(order => {
        const status = order.status;
        if (!statusMap.has(status)) {
          statusMap.set(status, { count: 0, qty: 0 });
        }
        const statusData = statusMap.get(status)!;
        statusData.count += 1;
        statusData.qty += order.qty || 0;
      });

      const statusBreakdown = Array.from(statusMap.entries()).map(([status, data]) => ({
        status,
        count: data.count,
        qty: data.qty
      }));

      // Monthly trend (last 6 months)
      const monthlyMap = new Map<string, { orders: number; amount: number; qty: number }>();
      const now = new Date();
      
      for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthKey = date.toISOString().substr(0, 7); // YYYY-MM format
        monthlyMap.set(monthKey, { orders: 0, amount: 0, qty: 0 });
      }

      allOrders.forEach(order => {
        const orderDate = new Date(order.date);
        const monthKey = orderDate.toISOString().substr(0, 7);
        if (monthlyMap.has(monthKey)) {
          const monthData = monthlyMap.get(monthKey)!;
          monthData.orders += 1;
          monthData.amount += order.total || 0;
          monthData.qty += order.qty || 0;
        }
      });

      const monthlyTrend = Array.from(monthlyMap.entries()).map(([month, data]) => ({
        month,
        orders: data.orders,
        amount: data.amount,
        qty: data.qty
      }));

      return {
        totalOrders: summary.totalOrders,
        totalAmount,
        totalQty,
        pendingAmount,
        completedAmount,
        pendingQty,
        completedQty,
        vendorBreakdown,
        consumableBreakdown,
        locationBreakdown,
        statusBreakdown,
        monthlyTrend
      };
    } catch (error) {
      console.error('Error fetching comprehensive stats:', error);
      throw error;
    }
  }

  // Get dashboard summary
  async getDashboardSummary(): Promise<{
    summary: TransactionOrderSummary;
    recentOrders: TransactionOrder[];
    pendingOrders: TransactionOrder[];
    topVendors: Array<{ vendor: string; count: number; amount: number; qty: number }>;
    topConsumables: Array<{ consumable: string; count: number; amount: number; qty: number }>;
    locationStats: Array<{ location: string; count: number; amount: number; qty: number }>;
  }> {
    try {
      const [summary, recentOrders, pendingOrders, stats] = await Promise.all([
        this.getOrderSummary(),
        this.getRecentOrders(),
        this.getPendingOrders({ limit: 5 }),
        this.getComprehensiveStats()
      ]);

      return {
        summary,
        recentOrders: recentOrders.slice(0, 5),
        pendingOrders: pendingOrders.data,
        topVendors: stats.vendorBreakdown
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 5),
        topConsumables: stats.consumableBreakdown
          .sort((a, b) => b.amount - a.amount)
          .slice(0, 5),
        locationStats: stats.locationBreakdown
          .sort((a, b) => b.amount - a.amount)
      };
    } catch (error) {
      console.error('Error fetching dashboard summary:', error);
      throw error;
    }
  }

  // Validate order data before submission
  validateOrderData(orderData: Partial<TransactionOrder>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!orderData.vendor?.trim()) {
      errors.push('Vendor is required');
    }

    if (!orderData.date) {
      errors.push('Date is required');
    }

    if (!orderData.Location?.trim()) {
      errors.push('Location is required');
    }

    if (!orderData.consumable?.trim()) {
      errors.push('Consumable is required');
    }

    if (!orderData.qty || orderData.qty <= 0) {
      errors.push('Quantity must be greater than 0');
    }

    if (!orderData.items || !Array.isArray(orderData.items) || orderData.items.length === 0) {
      errors.push('At least one item is required');
    } else {
      orderData.items.forEach((item, index) => {
        if (!item.item?.trim()) {
          errors.push(`Item ${index + 1}: Item name is required`);
        }
        if (!item.consumable?.trim()) {
          errors.push(`Item ${index + 1}: Consumable is required`);
        }
        if (!item.qty || item.qty <= 0) {
          errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
        }
        if (!item.amount || item.amount <= 0) {
          errors.push(`Item ${index + 1}: Amount must be greater than 0`);
        }
      });
    }

    if (orderData.total !== undefined && orderData.total <= 0) {
      errors.push('Total amount must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Calculate order total from items
  calculateOrderTotal(items: TransactionOrderItem[]): number {
    return items.reduce((total, item) => total + (item.amount || 0), 0);
  }

  // Calculate total quantity from items
  calculateItemsQuantity(items: TransactionOrderItem[]): number {
    return items.reduce((total, item) => total + (item.qty || 0), 0);
  }

  // Format order for display
  formatOrderForDisplay(order: TransactionOrder): TransactionOrder & {
    formattedDate: string;
    formattedTotal: string;
    itemCount: number;
    totalItemQty: number;
    formattedQty: string;
  } {
    const date = new Date(order.date);
    const formattedDate = date.toLocaleDateString();
    const formattedTotal = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(order.total || 0);
    
    const formattedQty = new Intl.NumberFormat('en-US').format(order.qty || 0);
    const itemCount = order.items?.length || 0;
    const totalItemQty = order.items?.reduce((sum, item) => sum + (item.qty || 0), 0) || 0;

    return {
      ...order,
      formattedDate,
      formattedTotal,
      formattedQty,
      itemCount,
      totalItemQty
    };
  }

  // Helper method to create order with calculated totals
  async createOrderWithCalculations(orderData: Omit<TransactionOrder, 'total' | 'id' | 'createdAt' | 'updatedAt'>): Promise<TransactionOrder> {
    const calculatedTotal = this.calculateOrderTotal(orderData.items);
    const orderWithTotal = {
      ...orderData,
      total: calculatedTotal
    };

    return this.createTransactionOrder(orderWithTotal);
  }

  // Helper method to update order with recalculated totals
  async updateOrderWithCalculations(id: number, orderData: Partial<TransactionOrder>): Promise<TransactionOrder> {
    if (orderData.items) {
      const calculatedTotal = this.calculateOrderTotal(orderData.items);
      orderData.total = calculatedTotal;
    }

    return this.updateTransactionOrder(id, orderData);
  }

  // Get quantity summary statistics
  async getQuantitySummary(): Promise<{
    totalQty: number;
    pendingQty: number;
    approvedQty: number;
    completedQty: number;
    cancelledQty: number;
  }> {
    try {
      const orders = await this.getTransactionOrders({ limit: 1000 });
      const allOrders = orders.data;
      
      const totalQty = allOrders.reduce((sum, order) => sum + (order.qty || 0), 0);
      const pendingQty = allOrders
        .filter(order => order.status === 'pending')
        .reduce((sum, order) => sum + (order.qty || 0), 0);
      const approvedQty = allOrders
        .filter(order => order.status === 'approved')
        .reduce((sum, order) => sum + (order.qty || 0), 0);
      const completedQty = allOrders
        .filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + (order.qty || 0), 0);
      const cancelledQty = allOrders
        .filter(order => order.status === 'cancelled')
        .reduce((sum, order) => sum + (order.qty || 0), 0);

      return {
        totalQty,
        pendingQty,
        approvedQty,
        completedQty,
        cancelledQty
      };
    } catch (error) {
      console.error('Error fetching quantity summary:', error);
      throw error;
    }
  }
}

export const transactionOrderService = new TransactionOrderService();
export default transactionOrderService;