// src/services/workflowService.ts
// Frontend API service for Asset Workflow Management

import {
  WorkflowTransition,
  AssetStatusTransitionRequest,
  AssetStatusTransitionResponse,
  WorkflowValidationRequest,
  WorkflowValidationResponse,
  ValidTransitionsResponse,
  AssetStatusHistoryResponse,
  WorkflowModule,
  AssetStatus,
  ApiResponse,
  PaginatedResponse
} from '../types/workflow';

// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class WorkflowService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/workflow${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || errorData?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Workflow API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Get valid transitions for an asset
  async getValidTransitions(assetId: number): Promise<ValidTransitionsResponse> {
    return this.makeRequest<ValidTransitionsResponse>(`/transitions?asset_id=${assetId}`);
  }

  // Execute an asset status transition
  async transitionAsset(request: AssetStatusTransitionRequest): Promise<AssetStatusTransitionResponse> {
    return this.makeRequest<AssetStatusTransitionResponse>('/transition', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // Validate a workflow action before execution
  async validateWorkflow(request: WorkflowValidationRequest): Promise<WorkflowValidationResponse> {
    const params = new URLSearchParams({
      asset_id: request.assetId.toString(),
      target_status: request.targetStatus,
      module: request.module,
    });

    if (request.context) {
      params.append('context', JSON.stringify(request.context));
    }

    return this.makeRequest<WorkflowValidationResponse>(`/validate?${params.toString()}`);
  }

  // Get asset status history
  async getAssetStatusHistory(
    assetId: number,
    page: number = 1,
    limit: number = 50
  ): Promise<PaginatedResponse<AssetStatusHistoryResponse>> {
    const params = new URLSearchParams({
      asset_id: assetId.toString(),
      page: page.toString(),
      limit: limit.toString(),
    });

    return this.makeRequest<PaginatedResponse<AssetStatusHistoryResponse>>(`/history?${params.toString()}`);
  }

  // Get all workflow state transitions (admin)
  async getAllWorkflowTransitions(): Promise<ApiResponse<WorkflowTransition[]>> {
    return this.makeRequest<ApiResponse<WorkflowTransition[]>>('/transitions/all');
  }

  // Batch status transition for multiple assets
  async batchTransitionAssets(requests: AssetStatusTransitionRequest[]): Promise<AssetStatusTransitionResponse[]> {
    return this.makeRequest<AssetStatusTransitionResponse[]>('/transition/batch', {
      method: 'POST',
      body: JSON.stringify({ transitions: requests }),
    });
  }

  // Get workflow statistics
  async getWorkflowStats(
    startDate?: string,
    endDate?: string,
    module?: WorkflowModule
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (module) params.append('module', module);

    const endpoint = params.toString() ? `/stats?${params.toString()}` : '/stats';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }

  // Helper methods for common workflows
  
  // Complete L&A Checklist workflow
  async completeLA(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.LA_CHECKLIST,
      notes,
    });
  }

  // Pack asset workflow
  async packAsset(assetId: number, packingListId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.PACKED,
      module: WorkflowModule.PACKING,
      context: packingListId ? { packingListId } : undefined,
      notes,
    });
  }

  // Unpack asset workflow
  async unpackAsset(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.PACKING,
      notes,
    });
  }

  // Check out asset workflow
  async checkoutAsset(
    assetId: number, 
    sessionId?: number, 
    notes?: string
  ): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.CHECKED_OUT,
      module: WorkflowModule.CHECKOUT,
      context: sessionId ? { sessionId } : undefined,
      notes,
    });
  }

  // Check in asset workflow
  async checkinAsset(
    assetId: number, 
    sessionId?: number, 
    notes?: string
  ): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.CHECKOUT,
      context: sessionId ? { sessionId } : undefined,
      notes,
    });
  }

  // Start maintenance workflow
  async startMaintenance(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.MAINTENANCE,
      module: WorkflowModule.MAINTENANCE,
      notes,
    });
  }

  // Complete maintenance workflow
  async completeMaintenance(assetId: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.READY,
      module: WorkflowModule.MAINTENANCE,
      notes,
    });
  }

  // Report damage workflow
  async reportDamage(assetId: number, reportId?: number, notes?: string): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: AssetStatus.DAMAGED,
      module: WorkflowModule.DAMAGE_REPORT,
      context: reportId ? { reportId } : undefined,
      notes,
    });
  }

  // Transfer asset workflow
  async transferAsset(
    assetId: number,
    transferId?: number,
    targetStatus?: AssetStatus,
    notes?: string
  ): Promise<AssetStatusTransitionResponse> {
    return this.transitionAsset({
      assetId,
      newStatus: targetStatus || AssetStatus.IN_USE,
      module: WorkflowModule.TRANSFER,
      context: transferId ? { transferId } : undefined,
      notes,
    });
  }

  // =================== L&A CHECKLIST METHODS ===================

  // Get L&A checklist sessions
  async getLAChecklistSessions(assetId?: number, status?: string): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    if (assetId) params.append('asset_id', assetId.toString());
    if (status) params.append('status', status);

    const endpoint = params.toString() ? `/la-checklist/sessions?${params.toString()}` : '/la-checklist/sessions';
    return this.makeRequest<ApiResponse<any[]>>(endpoint.replace('/workflow', ''));
  }

  // Get available assets for L&A testing
  async getAvailableAssetsForLA(): Promise<ApiResponse<any[]>> {
    return this.makeRequest<ApiResponse<any[]>>('/la-checklist/available-assets'.replace('/workflow', ''));
  }

  // Start L&A checklist session
  async startLASession(assetId: number, notes?: string): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>('/la-checklist/sessions'.replace('/workflow', ''), {
      method: 'POST',
      body: JSON.stringify({ asset_id: assetId, notes })
    });
  }

  // Complete L&A checklist session
  async completeLASession(sessionId: string, passed: boolean, notes?: string): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>(`/la-checklist/sessions/${sessionId}/complete`.replace('/workflow', ''), {
      method: 'PUT',
      body: JSON.stringify({ passed, notes })
    });
  }

  // =================== SUPPLY CHECKLIST METHODS ===================

  // Get supply checklists
  async getSupplyChecklists(type?: string, status?: string): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams();
    if (type) params.append('checklist_type', type);
    if (status) params.append('status', status);

    const endpoint = params.toString() ? `/supply-checklist/checklists?${params.toString()}` : '/supply-checklist/checklists';
    return this.makeRequest<ApiResponse<any[]>>(endpoint.replace('/workflow', ''));
  }

  // Create pack checklist
  async createPackChecklist(type: 'PACKING_LIST' | 'ROLLING_CAGE', assetIds: number[], notes?: string): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>('/supply-checklist/pack'.replace('/workflow', ''), {
      method: 'POST',
      body: JSON.stringify({
        checklist_type: type,
        asset_ids: assetIds,
        notes
      })
    });
  }

  // Create unpack checklist
  async createUnpackChecklist(type: 'PACKING_LIST' | 'ROLLING_CAGE', containerIds: string[], notes?: string): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>('/supply-checklist/unpack'.replace('/workflow', ''), {
      method: 'POST',
      body: JSON.stringify({
        checklist_type: type,
        container_ids: containerIds,
        notes
      })
    });
  }

  // =================== PACKING LIST METHODS ===================

  // Get packing lists
  async getPackingLists(): Promise<ApiResponse<any[]>> {
    return this.makeRequest<ApiResponse<any[]>>('/packing-lists'.replace('/workflow', ''));
  }

  // Delete packing list
  async deletePackingList(id: string): Promise<ApiResponse<void>> {
    return this.makeRequest<ApiResponse<void>>(`/packing-lists/${id}`.replace('/workflow', ''), {
      method: 'DELETE'
    });
  }

  // =================== TRANSACTION METHODS ===================

  // Get recent asset transactions
  async getRecentAssetTransactions(limit: number = 10): Promise<ApiResponse<any[]>> {
    return this.makeRequest<ApiResponse<any[]>>(`/asset-status-history/recent?limit=${limit}`.replace('/workflow', ''));
  }

  // Get recent consumable transactions
  async getRecentConsumableTransactions(limit: number = 10): Promise<ApiResponse<any[]>> {
    return this.makeRequest<ApiResponse<any[]>>(`/transaction-orders/recent?limit=${limit}`.replace('/workflow', ''));
  }
}

export const workflowService = new WorkflowService();
export default workflowService;
