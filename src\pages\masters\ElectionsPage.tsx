import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit, Eye, Plus, Trash, FileDown, Printer } from "lucide-react";
import { Link } from "react-router-dom";
import { ElectionTypeEditDialog } from "@/components/configuration/ElectionTypeEditDialog";
import { ElectionTypeViewDialog } from "@/components/configuration/ElectionTypeViewDialog";
import { ElectionType, ElectionTypeCreationData } from "@/services/electionTypeService";
import { electionService, Election, ElectionCreationData } from "@/services/electionService";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

// Confirm dialog interface
interface ConfirmDialogData {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  isDestructive?: boolean;
}

// Operation loading states interface
interface OperationStates {
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: Set<string>;
  bulkDeleting: boolean;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 8000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`flex items-center p-4 mb-3 rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out ${
            toast.type === 'success' 
              ? 'bg-green-50 border-green-500 text-green-800' 
              : 'bg-red-50 border-red-500 text-red-800'
          }`}
        >
          <span className="flex-1 text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className={`ml-3 p-1 rounded-full text-lg font-bold ${
              toast.type === 'success' ? 'hover:bg-green-200' : 'hover:bg-red-200'
            }`}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// API service
const API_BASE_URL = 'http://localhost:8000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

// Confirm Dialog Component
const ConfirmDialog: React.FC<ConfirmDialogData> = ({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = "Confirm",
  cancelText = "Cancel",
  isDestructive = false
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            className={`px-4 py-2 text-white rounded ${
              isDestructive 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

const ElectionTypePage = () => {
  const [electionTypes, setElectionTypes] = useState<ElectionType[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  
  // Dialog states
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedElectionType, setSelectedElectionType] = useState<ElectionType | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Multi-selection states
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Operation states
  const [operationStates, setOperationStates] = useState<OperationStates>({
    loading: true,
    creating: false,
    updating: false,
    deleting: new Set(),
    bulkDeleting: false,
  });

  // Confirm dialog state
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogData>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    onCancel: () => {},
  });

  console.log("🔍 ElectionTypePage rendering, loading:", operationStates.loading, "electionTypes:", electionTypes.length);

  // Load election types from backend
  const loadElectionTypes = async () => {
    try {
      console.log("🚀 Starting API call...");
      setOperationStates(prev => ({ ...prev, loading: true }));
      setError(null);
      
      const response = await apiCall('/election-types');
      console.log("📡 API Response:", response);
      
      if (response.success) {
        // Normalize date field names from backend
        const normalizedData = (response.data.electionTypes || []).map((type: any) => ({
          ...type,
          createdAt: type.created_at || type.createdAt,
          updatedAt: type.updated_at || type.updatedAt,
        }));
        setElectionTypes(normalizedData);
      } else {
        setError(response.message || 'Failed to load election types');
      }
    } catch (err) {
      console.error('❌ Error loading election types:', err);
      setError(err instanceof Error ? err.message : 'Failed to load election types');
    } finally {
      setOperationStates(prev => ({ ...prev, loading: false }));
      console.log("🏁 Loading finished");
    }
  };

  useEffect(() => {
    console.log("🎯 useEffect triggered");
    loadElectionTypes();
  }, []);

  // Handle add new election type
  const handleAddItem = () => {
    setSelectedElectionType(null);
    setIsEditMode(false);
    setEditDialogOpen(true);
  };

  // Handle view election type
  const handleViewItem = (electionType: ElectionType) => {
    setSelectedElectionType(electionType);
    setViewDialogOpen(true);
  };

  // Handle edit election type
  const handleEditItem = (electionType: ElectionType) => {
    setSelectedElectionType(electionType);
    setIsEditMode(true);
    setEditDialogOpen(true);
  };

  // Handle edit from view dialog
  const handleEditFromView = () => {
    setViewDialogOpen(false);
    setIsEditMode(true);
    setEditDialogOpen(true);
  };

  // Handle save election type (create or update)
  const handleSaveElectionType = async (electionTypeData: ElectionTypeCreationData) => {
    try {
      if (isEditMode && selectedElectionType) {
        // Update existing election type
        const response = await apiCall(`/election-types/${selectedElectionType.id}`, {
          method: 'PUT',
          body: JSON.stringify(electionTypeData),
        });

        if (response.success) {
          toast.success('Election type updated successfully!');
          setEditDialogOpen(false);
          loadElectionTypes();
        } else {
          toast.error(`Error: ${response.message}`);
        }
      } else {
        // Create new election type
        const response = await apiCall('/election-types', {
          method: 'POST',
          body: JSON.stringify(electionTypeData),
        });

        if (response.success) {
          toast.success('Election type created successfully!');
          setEditDialogOpen(false);
          loadElectionTypes();
        } else {
          toast.error(`Error: ${response.message}`);
        }
      }
    } catch (err) {
      console.error('Error saving election type:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  // Handle delete election type
  const handleDeleteItem = async (electionType: ElectionType) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Election Type',
      message: `Are you sure you want to delete "${electionType.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      isDestructive: true,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        await performDelete(electionType);
      },
      onCancel: () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
      },
    });
  };

  // Perform the actual delete operation
  const performDelete = async (electionType: ElectionType) => {
    try {
      setOperationStates(prev => ({
        ...prev,
        deleting: new Set([...prev.deleting, electionType.id])
      }));

      const response = await apiCall(`/election-types/${electionType.id}`, {
        method: 'DELETE',
      });

      if (response.success) {
        toast.success('Election type deleted successfully!');
        setSelectedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(electionType.id);
          return newSet;
        });
        loadElectionTypes();
      } else {
        toast.error(`Error: ${response.message}`);
      }
    } catch (err) {
      console.error('Error deleting election type:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setOperationStates(prev => {
        const newDeleting = new Set(prev.deleting);
        newDeleting.delete(electionType.id);
        return { ...prev, deleting: newDeleting };
      });
    }
  };

  // Handle multi-selection
  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems(new Set());
      setSelectAll(false);
    } else {
      setSelectedItems(new Set(filteredElectionTypes.map(item => item.id)));
      setSelectAll(true);
    }
  };

  const filteredElectionTypes = electionTypes.filter(type =>
    (type.name?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Update selectAll state when individual items are selected
  useEffect(() => {
    const allSelected = filteredElectionTypes.length > 0 && 
      filteredElectionTypes.every(item => selectedItems.has(item.id));
    setSelectAll(allSelected);
  }, [selectedItems, filteredElectionTypes]);

  // Handle bulk delete
  const handleBulkDelete = () => {
    const selectedTypes = electionTypes.filter(type => selectedItems.has(type.id));
    const typeNames = selectedTypes.map(type => type.name).join(', ');
    
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Multiple Election Types',
      message: `Are you sure you want to delete ${selectedItems.size} election type${selectedItems.size === 1 ? '' : 's'}? (${typeNames}) This action cannot be undone.`,
      confirmText: 'Delete All',
      cancelText: 'Cancel',
      isDestructive: true,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        await performBulkDelete();
      },
      onCancel: () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
      },
    });
  };

  // Perform bulk delete
  const performBulkDelete = async () => {
    setOperationStates(prev => ({ ...prev, bulkDeleting: true }));
    const selectedTypes = electionTypes.filter(type => selectedItems.has(type.id));
    
    try {
      const deletePromises = selectedTypes.map(async (type) => {
        const response = await apiCall(`/election-types/${type.id}`, {
          method: 'DELETE',
        });
        return { type, success: response.success, message: response.message };
      });

      const results = await Promise.all(deletePromises);
      const successful = results.filter(r => r.success);
      const failed = results.filter(r => !r.success);

      if (successful.length > 0) {
        toast.success(`Successfully deleted ${successful.length} election type${successful.length === 1 ? '' : 's'}`);
      }
      
      if (failed.length > 0) {
        toast.error(`Failed to delete ${failed.length} election type${failed.length === 1 ? '' : 's'}`);
      }

      setSelectedItems(new Set());
      setSelectAll(false);
      loadElectionTypes();
    } catch (err) {
      console.error('Error in bulk delete:', err);
      toast.error('Error during bulk delete operation');
    } finally {
      setOperationStates(prev => ({ ...prev, bulkDeleting: false }));
    }
  };

  console.log("🎨 About to render with state:", { loading: operationStates.loading, error, electionTypesCount: electionTypes.length });

  if (operationStates.loading) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between bg-gray-100 p-4 border-b">
            <h1 className="text-xl font-semibold">Election Type</h1>
            <div className="flex items-center text-sm text-gray-500">
              <Link to="/configuration" className="hover:underline">Configuration</Link>
              <span className="mx-2">›</span>
              <Link to="/configuration/settings" className="hover:underline">Settings</Link>
              <span className="mx-2">›</span>
              <span>Election Type</span>
            </div>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Election Types...
              </div>
              <p className="mt-2 text-gray-600">Please wait while we fetch the data.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between bg-gray-100 p-4 border-b">
            <h1 className="text-xl font-semibold">Election Type</h1>
            <div className="flex items-center text-sm text-gray-500">
              <Link to="/configuration" className="hover:underline">Configuration</Link>
              <span className="mx-2">›</span>
              <Link to="/configuration/settings" className="hover:underline">Settings</Link>
              <span className="mx-2">›</span>
              <span>Election Type</span>
            </div>
          </div>
          
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <div className="flex items-center mb-4">
                  <svg className="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Election Types</h3>
                </div>
                <p className="text-red-700 mb-4">{error}</p>
                <Button onClick={loadElectionTypes} className="bg-blue-500 hover:bg-blue-600">
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Retry
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between bg-gray-100 p-4 border-b">
          <h1 className="text-xl font-semibold">Election Type</h1>
          <div className="flex items-center text-sm text-gray-500">
            <Link to="/configuration" className="hover:underline">Configuration</Link>
            <span className="mx-2">›</span>
            <Link to="/configuration/settings" className="hover:underline">Settings</Link>
            <span className="mx-2">›</span>
            <span>Election Type</span>
          </div>
        </div>

        <div className="p-4">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Input
                type="text"
                placeholder="Search..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg
                className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            <div className="flex gap-2">
              {selectedItems.size > 0 && (
                <Button 
                  variant="destructive" 
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={operationStates.bulkDeleting}
                  className="bg-red-600 hover:bg-red-700"
                >
                  {operationStates.bulkDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash className="h-4 w-4 mr-2" />
                      Delete Selected ({selectedItems.size})
                    </>
                  )}
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export All
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => handleExport('pdf', filteredElectionTypes)}>
                    PDF Document
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('excel', filteredElectionTypes)}>
                    Excel Spreadsheet
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('csv', filteredElectionTypes)}>
                    CSV File
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button onClick={handleAddItem}>
                <Plus className="h-4 w-4 mr-2" />
                Add Election Type
              </Button>
            </div>
          </div>

          <div className="border rounded-lg shadow-sm overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-medium w-12">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="text-left py-3 px-4 font-medium">Election Type</th>
                  <th className="text-left py-3 px-4 font-medium">Status</th>
                  <th className="text-left py-3 px-4 font-medium">Created</th>
                  <th className="text-right py-3 px-4 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredElectionTypes.map((type) => (
                  <tr key={type.id} className="border-t hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <input
                        type="checkbox"
                        checked={selectedItems.has(type.id)}
                        onChange={() => handleSelectItem(type.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="py-3 px-4 font-medium">{type.name}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        type.status ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}>
                        {type.status ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-600">
                      {new Date(type.createdAt).toLocaleDateString('en-US', {
                        month: 'numeric',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <div className="flex gap-2 justify-end">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleViewItem(type)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleEditItem(type)}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteItem(type)}
                          disabled={operationStates.deleting.has(type.id)}
                        >
                          {operationStates.deleting.has(type.id) ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Deleting...
                            </>
                          ) : (
                            <Trash className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
                {filteredElectionTypes.length === 0 && (
                  <tr>
                    <td colSpan={5} className="p-4 text-center text-gray-500">
                      {searchTerm ? 'No matching records found' : 'No election types found'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          
          <div style={{ 
            marginTop: '20px', 
            padding: '10px', 
            backgroundColor: '#f0f0f0', 
            borderRadius: '4px',
            fontSize: '12px',
            color: '#666'
          }}>
            🛠️ Debug Info: {electionTypes.length} election types loaded from database!
          </div>
        </div>

        {/* Edit/Add Dialog */}
        <ElectionTypeEditDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          electionType={selectedElectionType}
          onSave={handleSaveElectionType}
          isEditMode={isEditMode}
        />

        {/* View Dialog */}
        <ElectionTypeViewDialog
          open={viewDialogOpen}
          onOpenChange={setViewDialogOpen}
          electionType={selectedElectionType}
          onEdit={handleEditFromView}
        />

        {/* Confirm Dialog */}
        <ConfirmDialog {...confirmDialog} />

        {/* Toast Container for success/error messages */}
        <ToastContainer />
      </div>
    </AppLayout>
  );
};

export default ElectionTypePage;

// Handle export data in various formats
const handleExport = (format: string, data: ElectionType[]) => {
  const exportableData = data.map(type => ({
    'Election Type': type.name,
    'Status': type.status ? 'Active' : 'Inactive',
    'Created': new Date(type.createdAt).toLocaleDateString(),
    'Last Updated': new Date(type.updatedAt).toLocaleDateString(),
    'ID': type.id,
  }));

  const headers = ['Election Type', 'Status', 'Created', 'Last Updated', 'ID'];

  exportData(exportableData, format, 'election_types', 'Election Types', headers);
};

// Handle print functionality
const handlePrint = (data: ElectionType[]) => {
  const printWindow = window.open('', '_blank');
  if (printWindow) {
    const now = new Date();
    const formattedDate = now.toLocaleDateString('en-US', { month: '2-digit', day: '2-digit', year: 'numeric' });
    const formattedTime = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false });
    const timestamp = `Generated on: ${formattedDate} ${formattedTime}`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Election Types</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
            .logo { max-height: 50px; }
            h1 { margin: 0; color: #333; font-size: 24px; }
            .timestamp { color: #666; font-size: 12px; margin-top: 5px; }
            table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; color: #333; }
            tr:nth-child(odd) { background-color: #f9f9f9; }
            tr:nth-child(even) { background-color: #ffffff; }
            .status-badge { display: inline-block; padding: 4px 8px; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; }
            .status-active { background-color: #d1fae5; color: #065f46; }
            .status-inactive { background-color: #fee2e2; color: #991b1b; }
            @media print { .button-container { display: none; } }
          </style>
        </head>
        <body>
          <div class="header">
            <div>
              <h1>Election Types</h1>
              <div class="timestamp">${timestamp}</div>
            </div>
            <img src="/logo.jpg" alt="Election Logo" class="logo" onerror="this.src='/assets/logo.jpg'; this.onerror=null;" />
          </div>

          <table>
            <thead>
              <tr>
                <th>Election Type</th>
                <th>Status</th>
                <th>Created On</th>
                <th>Last Updated</th>
                <th>ID</th>
              </tr>
            </thead>
            <tbody>
              ${data.map(electionType => `
                <tr>
                  <td>${electionType.name}</td>
                  <td>
                    <span class="status-badge ${electionType.status ? 'status-active' : 'status-inactive'}">
                      ${electionType.status ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td>${new Date(electionType.createdAt).toLocaleDateString()}</td>
                  <td>${new Date(electionType.updatedAt).toLocaleDateString()}</td>
                  <td>${electionType.id}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `;

    printWindow.document.open();
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.print();
  }
};