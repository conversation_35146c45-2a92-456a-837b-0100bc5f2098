#!/usr/bin/env python3
"""Simple script to run the packing lists table migration"""

import mysql.connector
import os
from urllib.parse import urlparse

def run_migration():
    """Run the SQL migration to add the election column."""
    
    # Parse database URL from environment or use default
    database_url = os.getenv('DATABASE_URL', 'mysql+mysqlconnector://root:root@localhost:3306/asset_management')
    
    # Parse the URL to get connection parameters
    parsed = urlparse(database_url.replace('mysql+mysqlconnector://', 'mysql://'))
    
    connection_config = {
        'host': parsed.hostname or 'localhost',
        'port': parsed.port or 3306,
        'user': parsed.username or 'root',
        'password': parsed.password or 'root',
        'database': parsed.path.lstrip('/') or 'asset_management'
    }
    
    try:
        # Connect to database
        print("Connecting to database...")
        conn = mysql.connector.connect(**connection_config)
        cursor = conn.cursor()
        
        # Check if column exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'packing_lists' 
            AND COLUMN_NAME = 'election'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if not column_exists:
            print("Adding missing 'election' column...")
            cursor.execute("""
                ALTER TABLE packing_lists 
                ADD COLUMN election VARCHAR(255) NOT NULL DEFAULT 'General Election 2024'
            """)
            conn.commit()
            print("✅ Successfully added 'election' column to packing_lists table")
        else:
            print("✅ 'election' column already exists in packing_lists table")
        
        # Verify the table structure
        cursor.execute("DESCRIBE packing_lists")
        columns = cursor.fetchall()
        print(f"\nCurrent packing_lists table columns: {len(columns)}")
        for col in columns:
            print(f"  - {col[0]} ({col[1]})")
            
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    print("Running packing lists table migration...")
    success = run_migration()
    if success:
        print("\n🎉 Migration completed successfully!")
    else:
        print("\n💥 Migration failed!") 