// src/services/checkoutSessionService.ts
// Frontend API service for Checkout Session Management

import {
  CheckoutSession,
  CheckoutSessionCreate,
  CheckoutSessionUpdate,
  CheckoutSessionFilters,
  CheckoutSessionAsset,
  CheckoutType,
  CheckoutStatus,
  ApiResponse,
  PaginatedResponse
} from '../types/workflow';

// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

class CheckoutSessionService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/checkout-sessions${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.error || errorData?.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Checkout Session API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Get all checkout sessions with filters and pagination
  async getCheckoutSessions(filters: CheckoutSessionFilters = {}): Promise<PaginatedResponse<CheckoutSession>> {
    const params = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.append(key, value.toString());
      }
    });

    const endpoint = params.toString() ? `?${params.toString()}` : '';
    return this.makeRequest<PaginatedResponse<CheckoutSession>>(endpoint);
  }

  // Get a specific checkout session by ID
  async getCheckoutSession(id: number): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}`);
  }

  // Create a new checkout session
  async createCheckoutSession(sessionData: CheckoutSessionCreate): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>('', {
      method: 'POST',
      body: JSON.stringify(sessionData),
    });
  }

  // Update an existing checkout session
  async updateCheckoutSession(id: number, updateData: CheckoutSessionUpdate): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  }

  // Delete a checkout session
  async deleteCheckoutSession(id: number): Promise<ApiResponse<null>> {
    return this.makeRequest<ApiResponse<null>>(`/${id}`, {
      method: 'DELETE',
    });
  }

  // Complete a checkout session (check in all assets)
  async completeCheckoutSession(
    id: number,
    conditionNotes?: string,
    notes?: string
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}/complete`, {
      method: 'POST',
      body: JSON.stringify({ conditionNotes, notes }),
    });
  }

  // Add assets to a checkout session
  async addAssetsToSession(
    id: number,
    assetIds: number[],
    conditionNotes?: string
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}/assets`, {
      method: 'POST',
      body: JSON.stringify({ assetIds, conditionNotes }),
    });
  }

  // Remove assets from a checkout session
  async removeAssetsFromSession(
    id: number,
    assetIds: number[]
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}/assets`, {
      method: 'DELETE',
      body: JSON.stringify({ assetIds }),
    });
  }

  // Check in specific assets
  async checkInAssets(
    id: number,
    assets: Array<{
      assetId: number;
      conditionAtReturn: string;
      damageNoted?: boolean;
      damageDescription?: string;
      notes?: string;
    }>
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.makeRequest<ApiResponse<CheckoutSession>>(`/${id}/checkin`, {
      method: 'POST',
      body: JSON.stringify({ assets }),
    });
  }

  // Get assets in a checkout session
  async getSessionAssets(id: number): Promise<ApiResponse<CheckoutSessionAsset[]>> {
    return this.makeRequest<ApiResponse<CheckoutSessionAsset[]>>(`/${id}/assets`);
  }

  // Update session status
  async updateSessionStatus(
    id: number,
    status: CheckoutStatus,
    notes?: string
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.updateCheckoutSession(id, { status, notes });
  }

  // Extend checkout session
  async extendCheckoutSession(
    id: number,
    newReturnDate: string,
    reason?: string
  ): Promise<ApiResponse<CheckoutSession>> {
    return this.updateCheckoutSession(id, {
      expectedReturnDate: newReturnDate,
      notes: reason,
    });
  }

  // Mark session as overdue
  async markSessionOverdue(id: number): Promise<ApiResponse<CheckoutSession>> {
    return this.updateSessionStatus(id, CheckoutStatus.OVERDUE);
  }

  // Cancel checkout session
  async cancelCheckoutSession(id: number, reason?: string): Promise<ApiResponse<CheckoutSession>> {
    return this.updateCheckoutSession(id, {
      status: CheckoutStatus.CANCELLED,
      notes: reason,
    });
  }

  // Get active checkout sessions
  async getActiveCheckoutSessions(): Promise<ApiResponse<CheckoutSession[]>> {
    return this.getCheckoutSessions({
      status: CheckoutStatus.ACTIVE,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get overdue checkout sessions
  async getOverdueCheckoutSessions(): Promise<ApiResponse<CheckoutSession[]>> {
    return this.getCheckoutSessions({
      status: CheckoutStatus.OVERDUE,
      sortBy: 'expected_return_date',
      sortOrder: 'ASC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get checkout sessions by type
  async getCheckoutSessionsByType(checkoutType: CheckoutType): Promise<ApiResponse<CheckoutSession[]>> {
    return this.getCheckoutSessions({
      checkoutType,
      sortBy: 'created_at',
      sortOrder: 'DESC',
    }).then(response => ({
      success: response.success,
      data: response.data,
      message: response.message,
    }));
  }

  // Get checkout sessions by user
  async getCheckoutSessionsByUser(userId: number): Promise<ApiResponse<CheckoutSession[]>> {
    return this.makeRequest<ApiResponse<CheckoutSession[]>>(`/user/${userId}`);
  }

  // Get checkout statistics
  async getCheckoutStats(
    startDate?: string,
    endDate?: string,
    checkoutType?: CheckoutType
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (checkoutType) params.append('checkout_type', checkoutType);

    const endpoint = params.toString() ? `/stats?${params.toString()}` : '/stats';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }

  // Bulk update checkout sessions
  async bulkUpdateCheckoutSessions(
    sessionIds: number[],
    updateData: Partial<CheckoutSessionUpdate>
  ): Promise<ApiResponse<CheckoutSession[]>> {
    return this.makeRequest<ApiResponse<CheckoutSession[]>>('/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({
        sessionIds,
        updateData,
      }),
    });
  }

  // Get checkout history for reporting
  async getCheckoutHistory(
    startDate: string,
    endDate: string,
    format: 'json' | 'csv' = 'json'
  ): Promise<any> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
      format,
    });

    return this.makeRequest<any>(`/history?${params.toString()}`);
  }

  // Generate checkout documentation
  async generateCheckoutDocument(id: number, template: string = 'default'): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>(`/${id}/document`, {
      method: 'POST',
      body: JSON.stringify({ template }),
    });
  }

  // Send checkout reminder
  async sendCheckoutReminder(id: number, message?: string): Promise<ApiResponse<any>> {
    return this.makeRequest<ApiResponse<any>>(`/${id}/reminder`, {
      method: 'POST',
      body: JSON.stringify({ message }),
    });
  }

  // Get asset utilization report
  async getAssetUtilizationReport(
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    const endpoint = params.toString() ? `/utilization?${params.toString()}` : '/utilization';
    return this.makeRequest<ApiResponse<any>>(endpoint);
  }
}

export const checkoutSessionService = new CheckoutSessionService();
export default checkoutSessionService;
