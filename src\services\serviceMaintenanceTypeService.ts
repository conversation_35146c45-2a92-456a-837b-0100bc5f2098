// Frontend Service Maintenance Type Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface ServiceMaintenanceType {
  id: string;
  name: string;
  description: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceMaintenanceTypeCreationData {
  name: string;
  description: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class ServiceMaintenanceTypeService {
  // Get all service maintenance types
  async getAllServiceMaintenanceTypes(): Promise<{ serviceMaintenanceTypes: ServiceMaintenanceType[]; count: number }> {
    try {
      const response = await apiCall('/service-maintenance-types');
      if (response.success) {
        return {
          serviceMaintenanceTypes: response.data.serviceMaintenanceTypes,
          count: response.data.count
        };
      }
      throw new Error(response.message || 'Failed to fetch service maintenance types');
    } catch (error) {
      console.error('Error fetching service maintenance types:', error);
      throw error;
    }
  }

  // Get service maintenance type by ID
  async getServiceMaintenanceTypeById(id: string): Promise<ServiceMaintenanceType> {
    try {
      const response = await apiCall(`/service-maintenance-types/${id}`);
      if (response.success) {
        return response.data.serviceMaintenanceType;
      }
      throw new Error(response.message || 'Failed to fetch service maintenance type');
    } catch (error) {
      console.error('Error fetching service maintenance type by ID:', error);
      throw error;
    }
  }

  // Create new service maintenance type
  async createServiceMaintenanceType(data: ServiceMaintenanceTypeCreationData): Promise<ServiceMaintenanceType> {
    try {
      const response = await apiCall('/service-maintenance-types', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      if (response.success) {
        return response.data.serviceMaintenanceType;
      }
      throw new Error(response.message || 'Failed to create service maintenance type');
    } catch (error) {
      console.error('Error creating service maintenance type:', error);
      throw error;
    }
  }

  // Update service maintenance type
  async updateServiceMaintenanceType(id: string, data: Partial<ServiceMaintenanceTypeCreationData>): Promise<ServiceMaintenanceType> {
    try {
      const response = await apiCall(`/service-maintenance-types/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
      if (response.success) {
        return response.data.serviceMaintenanceType;
      }
      throw new Error(response.message || 'Failed to update service maintenance type');
    } catch (error) {
      console.error('Error updating service maintenance type:', error);
      throw error;
    }
  }

  // Delete service maintenance type
  async deleteServiceMaintenanceType(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/service-maintenance-types/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting service maintenance type:', error);
      throw error;
    }
  }

  // Helper method to prepare service maintenance types for export
  prepareServiceMaintenanceTypesForExport(types: ServiceMaintenanceType[]) {
    return types.map(type => ({
      ID: type.id,
      'Name': type.name,
      'Description': type.description,
      'Status': type.status ? 'Active' : 'Inactive',
      'Created At': new Date(type.createdAt).toLocaleDateString(),
      'Updated At': new Date(type.updatedAt).toLocaleDateString()
    }));
  }
}

const serviceMaintenanceTypeService = new ServiceMaintenanceTypeService();
export default serviceMaintenanceTypeService; 