#!/usr/bin/env python3
"""
Test script to validate the RBAC implementation.
This script tests the complete RBAC system including database migration,
user creation, asset access, and transfer permissions.
"""

import sys
import os
import asyncio
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import DATABASE_URL, get_db
from app.models.user import User
from app.models.assets import Asset
from app.models.asset_transfers import AssetTransfer, TransferStatus, TransferType
from app.middleware.rbac import RBACService
from uuid import uuid4
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_users(db):
    """Create test users with different access levels."""
    logger.info("Creating test users...")
    
    users = [
        {
            'id': str(uuid4()),
            'first_name': 'Admin',
            'last_name': 'User',
            'email': '<EMAIL>',
            'password': 'hashed_password',
            'mobile': '1234567890',
            'user_group': 'Admin Group',
            'login_id': 'admin_test',
            'access_level': 'state',
            'state': 'South Carolina',
            'role': 'admin',
            'status': True,
            'login_enabled': True
        },
        {
            'id': str(uuid4()),
            'first_name': 'State',
            'last_name': 'Manager',
            'email': '<EMAIL>',
            'password': 'hashed_password',
            'mobile': '1234567891',
            'user_group': 'State Group',
            'login_id': 'state_test',
            'access_level': 'state',
            'state': 'South Carolina',
            'role': 'manager',
            'status': True,
            'login_enabled': True
        },
        {
            'id': str(uuid4()),
            'first_name': 'County',
            'last_name': 'Manager',
            'email': '<EMAIL>',
            'password': 'hashed_password',
            'mobile': '1234567892',
            'user_group': 'County Group',
            'login_id': 'county_test',
            'access_level': 'county',
            'state': 'South Carolina',
            'county': 'Charleston',
            'role': 'manager',
            'status': True,
            'login_enabled': True
        },
        {
            'id': str(uuid4()),
            'first_name': 'Precinct',
            'last_name': 'User',
            'email': '<EMAIL>',
            'password': 'hashed_password',
            'mobile': '1234567893',
            'user_group': 'Precinct Group',
            'login_id': 'precinct_test',
            'access_level': 'precinct',
            'state': 'South Carolina',
            'county': 'Charleston',
            'precinct': 'Precinct 1',
            'role': 'user',
            'status': True,
            'login_enabled': True
        }
    ]
    
    created_users = {}
    for user_data in users:
        user = User(**user_data)
        db.add(user)
        created_users[user_data['access_level'] + '_' + user_data['role']] = user
    
    db.commit()
    logger.info(f"Created {len(users)} test users")
    return created_users

def create_test_assets(db):
    """Create test assets in different geographical locations."""
    logger.info("Creating test assets...")
    
    assets = [
        {
            'id': 1001,
            'asset_id': 'TEST-ASSET-001',
            'type': 'Voting Machine',
            'state': 'South Carolina',
            'county': 'Charleston',
            'precinct': 'Precinct 1',
            'location': 'Charleston Warehouse',
            'status': 'Ready'
        },
        {
            'id': 1002,
            'asset_id': 'TEST-ASSET-002',
            'type': 'Voting Machine',
            'state': 'South Carolina',
            'county': 'Charleston',
            'precinct': 'Precinct 2',
            'location': 'Charleston Warehouse',
            'status': 'Ready'
        },
        {
            'id': 1003,
            'asset_id': 'TEST-ASSET-003',
            'type': 'Voting Machine',
            'state': 'South Carolina',
            'county': 'Greenville',
            'precinct': 'Precinct 1',
            'location': 'Greenville Warehouse',
            'status': 'Ready'
        },
        {
            'id': 1004,
            'asset_id': 'TEST-ASSET-004',
            'type': 'Voting Machine',
            'state': 'North Carolina',
            'county': 'Wake',
            'precinct': 'Precinct 1',
            'location': 'Wake Warehouse',
            'status': 'Ready'
        }
    ]
    
    created_assets = []
    for asset_data in assets:
        asset = Asset(**asset_data)
        db.add(asset)
        created_assets.append(asset)
    
    db.commit()
    logger.info(f"Created {len(assets)} test assets")
    return created_assets

def test_user_scope_validation(db, users):
    """Test user geographical scope validation."""
    logger.info("Testing user scope validation...")
    
    test_results = []
    
    for user_key, user in users.items():
        logger.info(f"Testing user: {user_key}")
        
        # Test geographical scope validation
        is_valid = user.validate_geographical_scope()
        test_results.append({
            'user': user_key,
            'scope_valid': is_valid,
            'access_level': user.access_level,
            'state': user.state,
            'county': user.county,
            'precinct': user.precinct
        })
        
        logger.info(f"  - Scope valid: {is_valid}")
        logger.info(f"  - Access level: {user.access_level}")
        logger.info(f"  - Geographical scope: {user.state}/{user.county}/{user.precinct}")
    
    return test_results

def test_asset_access_control(db, users, assets):
    """Test asset access control based on user roles."""
    logger.info("Testing asset access control...")
    
    test_results = []
    
    for user_key, user in users.items():
        logger.info(f"Testing asset access for user: {user_key}")
        
        rbac = RBACService(db, user)
        
        # Test query filtering
        query = db.query(Asset).filter(Asset.id.in_([asset.id for asset in assets]))
        filtered_query = rbac.apply_asset_filters(query)
        accessible_assets = filtered_query.all()
        
        # Test individual asset access
        individual_access = []
        for asset in assets:
            can_access = rbac.can_access_asset(asset)
            individual_access.append({
                'asset_id': asset.asset_id,
                'can_access': can_access,
                'asset_location': f"{asset.state}/{asset.county}/{asset.precinct}"
            })
        
        test_results.append({
            'user': user_key,
            'accessible_assets_count': len(accessible_assets),
            'accessible_assets': [a.asset_id for a in accessible_assets],
            'individual_access': individual_access
        })
        
        logger.info(f"  - Can access {len(accessible_assets)} assets via query filter")
        logger.info(f"  - Accessible assets: {[a.asset_id for a in accessible_assets]}")
    
    return test_results

def test_transfer_permissions(db, users, assets):
    """Test transfer permission validation."""
    logger.info("Testing transfer permissions...")
    
    test_results = []
    
    # Test transfer scenarios
    transfer_scenarios = [
        {
            'from': {'state': 'South Carolina', 'county': 'Charleston', 'precinct': 'Precinct 1'},
            'to': {'state': 'South Carolina', 'county': 'Charleston', 'precinct': 'Precinct 2'}
        },
        {
            'from': {'state': 'South Carolina', 'county': 'Charleston', 'precinct': 'Precinct 1'},
            'to': {'state': 'South Carolina', 'county': 'Greenville', 'precinct': 'Precinct 1'}
        },
        {
            'from': {'state': 'South Carolina', 'county': 'Charleston', 'precinct': 'Precinct 1'},
            'to': {'state': 'North Carolina', 'county': 'Wake', 'precinct': 'Precinct 1'}
        }
    ]
    
    for user_key, user in users.items():
        logger.info(f"Testing transfer permissions for user: {user_key}")
        
        rbac = RBACService(db, user)
        user_results = []
        
        for i, scenario in enumerate(transfer_scenarios):
            can_initiate = rbac.can_initiate_transfer(scenario['from'], scenario['to'])
            user_results.append({
                'scenario': i + 1,
                'from': scenario['from'],
                'to': scenario['to'],
                'can_initiate': can_initiate
            })
            
            logger.info(f"  - Scenario {i + 1}: {can_initiate}")
        
        test_results.append({
            'user': user_key,
            'transfer_tests': user_results
        })
    
    return test_results

def cleanup_test_data(db, users, assets):
    """Clean up test data."""
    logger.info("Cleaning up test data...")
    
    # Delete test assets
    for asset in assets:
        db.delete(asset)
    
    # Delete test users
    for user in users.values():
        db.delete(user)
    
    db.commit()
    logger.info("Test data cleaned up")

def main():
    """Main test function."""
    logger.info("Starting RBAC implementation validation...")
    
    # Create database session
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Create test data
        users = create_test_users(db)
        assets = create_test_assets(db)
        
        # Run tests
        scope_results = test_user_scope_validation(db, users)
        access_results = test_asset_access_control(db, users, assets)
        transfer_results = test_transfer_permissions(db, users, assets)
        
        # Print summary
        logger.info("\n" + "="*50)
        logger.info("RBAC VALIDATION SUMMARY")
        logger.info("="*50)
        
        logger.info("\n1. User Scope Validation:")
        for result in scope_results:
            logger.info(f"   {result['user']}: {'✓' if result['scope_valid'] else '✗'}")
        
        logger.info("\n2. Asset Access Control:")
        for result in access_results:
            logger.info(f"   {result['user']}: {result['accessible_assets_count']} assets accessible")
        
        logger.info("\n3. Transfer Permissions:")
        for result in transfer_results:
            allowed_transfers = sum(1 for t in result['transfer_tests'] if t['can_initiate'])
            logger.info(f"   {result['user']}: {allowed_transfers}/3 transfers allowed")
        
        logger.info("\n" + "="*50)
        logger.info("RBAC validation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during RBAC validation: {e}")
        raise
    finally:
        # Clean up
        if 'users' in locals() and 'assets' in locals():
            cleanup_test_data(db, users, assets)
        db.close()

if __name__ == "__main__":
    main()
