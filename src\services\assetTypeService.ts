// Frontend Asset Type Service
// Fix for Vite environment variables
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

export interface AssetType {
  id: string;
  name: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AssetTypeCreationData {
  name: string;
  status: boolean;
}

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : '',
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
};

class AssetTypeService {
  // Get all asset types
  async getAllAssetTypes(): Promise<{ assetTypes: AssetType[]; count: number }> {
    try {
      const response = await apiCall('/asset-types');
      if (response.success) {
        return {
          assetTypes: response.data.assetTypes,
          count: response.data.count
        };
      }
      throw new Error(response.message || 'Failed to fetch asset types');
    } catch (error) {
      console.error('Error fetching asset types:', error);
      throw error;
    }
  }

  // Get asset type by ID
  async getAssetTypeById(id: string): Promise<AssetType> {
    try {
      const response = await apiCall(`/asset-types/${id}`);
      if (response.success) {
        return response.data.assetType;
      }
      throw new Error(response.message || 'Failed to fetch asset type');
    } catch (error) {
      console.error('Error fetching asset type by ID:', error);
      throw error;
    }
  }

  // Create new asset type
  async createAssetType(assetTypeData: AssetTypeCreationData): Promise<AssetType> {
    try {
      const response = await apiCall('/asset-types', {
        method: 'POST',
        body: JSON.stringify(assetTypeData),
      });
      if (response.success) {
        return response.data.assetType;
      }
      throw new Error(response.message || 'Failed to create asset type');
    } catch (error) {
      console.error('Error creating asset type:', error);
      throw error;
    }
  }

  // Update asset type
  async updateAssetType(id: string, assetTypeData: Partial<AssetTypeCreationData>): Promise<AssetType> {
    try {
      const response = await apiCall(`/asset-types/${id}`, {
        method: 'PUT',
        body: JSON.stringify(assetTypeData),
      });
      if (response.success) {
        return response.data.assetType;
      }
      throw new Error(response.message || 'Failed to update asset type');
    } catch (error) {
      console.error('Error updating asset type:', error);
      throw error;
    }
  }

  // Delete asset type
  async deleteAssetType(id: string): Promise<boolean> {
    try {
      const response = await apiCall(`/asset-types/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting asset type:', error);
      throw error;
    }
  }

  // Helper method to prepare asset types for export
  prepareAssetTypesForExport(assetTypes: AssetType[]) {
    return assetTypes.map(assetType => ({
      ID: assetType.id,
      'Asset Type': assetType.name,
      'Status': assetType.status ? 'Active' : 'Inactive',
      'Created At': new Date(assetType.createdAt).toLocaleDateString(),
      'Updated At': new Date(assetType.updatedAt).toLocaleDateString()
    }));
  }
}

const assetTypeService = new AssetTypeService();
export default assetTypeService;