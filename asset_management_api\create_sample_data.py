#!/usr/bin/env python3
"""
Create sample data for testing the asset management system.
This script creates sample assets, asset types, and other necessary data.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from app.models.assets import Asset, AssetStatus, AssetCondition
from app.models.asset_type import AssetType
from app.models.asset_model import AssetModel
from app.models.asset_status import AssetStatus as AssetStatusConfig
from app.models.masters.location import Location
from sqlalchemy.orm import Session
import uuid
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_sample_data():
    """Create sample data for testing."""
    
    db = next(get_db())
    
    try:
        # Create Asset Types
        asset_types = [
            {"id": str(uuid.uuid4()), "name": "Electronic Voting Machine", "status": True},
            {"id": str(uuid.uuid4()), "name": "VVPAT Unit", "status": True},
            {"id": str(uuid.uuid4()), "name": "Ballot Box", "status": True},
            {"id": str(uuid.uuid4()), "name": "Scanner", "status": True},
            {"id": str(uuid.uuid4()), "name": "Printer", "status": True},
            {"id": str(uuid.uuid4()), "name": "Laptop", "status": True},
            {"id": str(uuid.uuid4()), "name": "Power Cable", "status": True},
            {"id": str(uuid.uuid4()), "name": "Security Seal", "status": True},
        ]

        for asset_type_data in asset_types:
            existing = db.query(AssetType).filter(AssetType.name == asset_type_data["name"]).first()
            if not existing:
                asset_type = AssetType(**asset_type_data)
                db.add(asset_type)
                logger.info(f"Created asset type: {asset_type_data['name']}")
        
        # Create Asset Models
        asset_models = [
            {"id": str(uuid.uuid4()), "model_name": "EVM-2024-A", "asset_type": "Electronic Voting Machine", "manufacturer": "VoteTech", "status": True},
            {"id": str(uuid.uuid4()), "model_name": "VVPAT-2024-B", "asset_type": "VVPAT Unit", "manufacturer": "VoteTech", "status": True},
            {"id": str(uuid.uuid4()), "model_name": "BB-Standard", "asset_type": "Ballot Box", "manufacturer": "SecureVote", "status": True},
            {"id": str(uuid.uuid4()), "model_name": "Scanner-Pro", "asset_type": "Scanner", "manufacturer": "ScanTech", "status": True},
            {"id": str(uuid.uuid4()), "model_name": "HP-LaserJet", "asset_type": "Printer", "manufacturer": "HP", "status": True},
            {"id": str(uuid.uuid4()), "model_name": "Dell-Latitude", "asset_type": "Laptop", "manufacturer": "Dell", "status": True},
        ]

        for asset_model_data in asset_models:
            existing = db.query(AssetModel).filter(AssetModel.model_name == asset_model_data["model_name"]).first()
            if not existing:
                asset_model = AssetModel(**asset_model_data)
                db.add(asset_model)
                logger.info(f"Created asset model: {asset_model_data['model_name']}")
        
        # Create Asset Status configurations
        asset_statuses = [
            {"id": str(uuid.uuid4()), "asset_status": "New", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Ready", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Failed", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Packed", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Checked-out", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "In-transfer", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Delivered", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Using", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Damaged", "status": True},
            {"id": str(uuid.uuid4()), "asset_status": "Under Maintenance", "status": True},
        ]
        
        for status_data in asset_statuses:
            existing = db.query(AssetStatusConfig).filter(AssetStatusConfig.asset_status == status_data["asset_status"]).first()
            if not existing:
                status_config = AssetStatusConfig(**status_data)
                db.add(status_config)
                logger.info(f"Created asset status: {status_data['asset_status']}")
        
        db.commit()
        
        # Create Sample Assets
        sample_assets = [
            {
                "asset_id": "EVM-001",
                "type": "Electronic Voting Machine",
                "model": "EVM-2024-A",
                "serial_number": "EVM001SN",
                "status": AssetStatus.NEW,
                "condition": AssetCondition.GOOD,
                "location": "Central Warehouse",
                "state": "Georgia",
                "county": "Fulton",
                "precinct": "Precinct 001"
            },
            {
                "asset_id": "EVM-002",
                "type": "Electronic Voting Machine",
                "model": "EVM-2024-A",
                "serial_number": "EVM002SN",
                "status": AssetStatus.NEW,
                "condition": AssetCondition.GOOD,
                "location": "Central Warehouse",
                "state": "Georgia",
                "county": "Fulton",
                "precinct": "Precinct 002"
            },
            {
                "asset_id": "VVPAT-001",
                "type": "VVPAT Unit",
                "model": "VVPAT-2024-B",
                "serial_number": "VVPAT001SN",
                "status": AssetStatus.NEW,
                "condition": AssetCondition.GOOD,
                "location": "Central Warehouse",
                "state": "Georgia",
                "county": "Fulton",
                "precinct": "Precinct 001"
            },
            {
                "asset_id": "SCANNER-001",
                "type": "Scanner",
                "model": "Scanner-Pro",
                "serial_number": "SCAN001SN",
                "status": AssetStatus.NEW,
                "condition": AssetCondition.GOOD,
                "location": "Central Warehouse",
                "state": "Georgia",
                "county": "Fulton",
                "precinct": "Precinct 003"
            },
            {
                "asset_id": "LAPTOP-001",
                "type": "Laptop",
                "model": "Dell-Latitude",
                "serial_number": "LAPTOP001SN",
                "status": AssetStatus.READY,
                "condition": AssetCondition.GOOD,
                "location": "IT Department",
                "state": "Georgia",
                "county": "Fulton",
                "precinct": "Precinct 001"
            }
        ]
        
        for asset_data in sample_assets:
            existing = db.query(Asset).filter(Asset.asset_id == asset_data["asset_id"]).first()
            if not existing:
                asset = Asset(**asset_data)
                db.add(asset)
                logger.info(f"Created asset: {asset_data['asset_id']}")
        
        db.commit()
        logger.info("🎉 Sample data created successfully!")
        
    except Exception as e:
        logger.error(f"❌ Error creating sample data: {e}")
        db.rollback()
        return False
    finally:
        db.close()
        
    return True

if __name__ == "__main__":
    print("🔧 Creating sample data for testing...")
    success = create_sample_data()
    if success:
        print("✅ Sample data created successfully!")
    else:
        print("❌ Failed to create sample data. Check logs above.")
        sys.exit(1)
