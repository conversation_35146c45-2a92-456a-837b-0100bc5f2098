# app/models/assets.py
from sqlalchemy import Column, Integer, String, DateTime, Enum, Text
from sqlalchemy.sql import func
from app.config.database import Base
import enum
import json
from typing import Optional, Dict, Any

# Status enum - Updated for workflow
class AssetStatus(str, enum.Enum):
    NEW = "New"
    READY = "Ready"
    FAILED = "Failed"
    PACKED = "Packed"
    CHECKED_OUT = "Checked-out"
    IN_TRANSFER = "In-transfer"
    DELIVERED = "Delivered"
    USING = "Using"
    DAMAGED = "Damaged"
    UNDER_MAINTENANCE = "Under Maintenance"
    COMPLETED = "Completed"
    RETIRED = "Retired"

# Condition enum
class AssetCondition(str, enum.Enum):
    EXCELLENT = "Excellent"
    GOOD = "Good"
    FAIR = "Fair"
    POOR = "Poor"
    DAMAGED = "Damaged"

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    asset_id = Column(String(100), nullable=False, unique=True, index=True)
    type = Column(String(255), nullable=False)
    model = Column(String(255), nullable=True)
    serial_number = Column(String(255), nullable=True)
    status = Column(Enum(AssetStatus), nullable=False, default=AssetStatus.NEW)
    condition = Column(Enum(AssetCondition), nullable=False, default=AssetCondition.GOOD)
    location = Column(String(255), nullable=False)
    assigned_to = Column(String(255), nullable=True)

    # Geographical hierarchy for RBAC
    state = Column(String(100), nullable=False, index=True)  # Required for all assets
    county = Column(String(100), nullable=False, index=True)  # Required for all assets
    precinct = Column(String(100), nullable=True, index=True)  # Optional, for precinct-level assets
    
    # Dates
    purchase_date = Column(DateTime, nullable=True)
    warranty_expiry = Column(DateTime, nullable=True)
    last_maintenance = Column(DateTime, nullable=True)
    next_maintenance = Column(DateTime, nullable=True)
    last_checked = Column(DateTime, nullable=True)
    
    # Additional metadata
    notes = Column(Text, nullable=True)
    specifications = Column(Text, nullable=True)  # JSON string for technical specs
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def get_specifications_data(self) -> Dict[str, Any]:
        """Get specifications as dictionary."""
        try:
            return json.loads(self.specifications) if self.specifications else {}
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_specifications_data(self, specs: Dict[str, Any]):
        """Set specifications from dictionary."""
        try:
            self.specifications = json.dumps(specs) if specs else None
        except (TypeError, ValueError):
            self.specifications = None

    def to_dict(self) -> dict:
        """Convert asset object to dictionary."""
        return {
            "id": self.id,
            "asset_id": self.asset_id,
            "type": self.type,
            "model": self.model,
            "serial_number": self.serial_number,
            "status": self.status,
            "condition": self.condition,
            "location": self.location,
            "assigned_to": self.assigned_to,
            "county": self.county,
            "precinct": self.precinct,
            "purchase_date": self.purchase_date,
            "warranty_expiry": self.warranty_expiry,
            "last_maintenance": self.last_maintenance,
            "next_maintenance": self.next_maintenance,
            "last_checked": self.last_checked,
            "notes": self.notes,
            "specifications": self.get_specifications_data(),
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @staticmethod
    def generate_asset_id(asset_type: str, count: int) -> str:
        """Generate unique asset ID based on type and count."""
        type_prefix = asset_type.upper()[:3]
        next_number = str(count + 1).zfill(3)
        return f"{type_prefix}-{next_number}"