#!/usr/bin/env python3
"""
Create missing workflow tables for the asset management system.
This script creates tables without foreign key constraints to avoid dependency issues.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import engine
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_missing_tables():
    """Create missing workflow tables."""
    
    # SQL statements for missing tables
    tables_sql = {
        "supply_checklists": """
        CREATE TABLE IF NOT EXISTS supply_checklists (
            id INTEGER NOT NULL AUTO_INCREMENT,
            checklist_id VARCHAR(100) NOT NULL UNIQUE,
            checklist_type ENUM('Pack','Unpack') NOT NULL,
            status ENUM('Pending','In Progress','Completed','Cancelled') NOT NULL DEFAULT 'Pending',
            created_by VARCHAR(36) NOT NULL,
            packing_list_id INTEGER NULL,
            rolling_cage_id VARCHAR(36) NULL,
            election_id VARCHAR(100) NULL,
            location VARCHAR(255) NULL,
            notes TEXT NULL,
            started_at DATETIME NULL,
            completed_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_checklist_id (checklist_id),
            INDEX idx_created_by (created_by),
            INDEX idx_status (status),
            INDEX idx_checklist_type (checklist_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        
        "supply_checklist_items": """
        CREATE TABLE IF NOT EXISTS supply_checklist_items (
            id INTEGER NOT NULL AUTO_INCREMENT,
            checklist_id INTEGER NOT NULL,
            asset_id INTEGER NOT NULL,
            previous_status VARCHAR(50) NULL,
            new_status VARCHAR(50) NOT NULL,
            processed_by VARCHAR(36) NULL,
            notes TEXT NULL,
            processed_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_checklist_id (checklist_id),
            INDEX idx_asset_id (asset_id),
            INDEX idx_processed_by (processed_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        
        "la_checklist_sessions": """
        CREATE TABLE IF NOT EXISTS la_checklist_sessions (
            id INTEGER NOT NULL AUTO_INCREMENT,
            session_id VARCHAR(100) NOT NULL UNIQUE,
            asset_id INTEGER NOT NULL,
            status ENUM('Pending','In Progress','Completed','Failed','Cancelled') NOT NULL DEFAULT 'Pending',
            created_by VARCHAR(36) NOT NULL,
            assigned_to VARCHAR(36) NULL,
            election_id VARCHAR(100) NULL,
            location VARCHAR(255) NULL,
            notes TEXT NULL,
            started_at DATETIME NULL,
            completed_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_session_id (session_id),
            INDEX idx_asset_id (asset_id),
            INDEX idx_created_by (created_by),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        
        "la_checklist_items": """
        CREATE TABLE IF NOT EXISTS la_checklist_items (
            id INTEGER NOT NULL AUTO_INCREMENT,
            session_id INTEGER NOT NULL,
            item_name VARCHAR(255) NOT NULL,
            item_description TEXT NULL,
            is_required BOOLEAN NOT NULL DEFAULT TRUE,
            status ENUM('Pending','Pass','Fail','N/A') NOT NULL DEFAULT 'Pending',
            checked_by VARCHAR(36) NULL,
            comments TEXT NULL,
            checked_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_session_id (session_id),
            INDEX idx_checked_by (checked_by),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """,
        
        "asset_status_history": """
        CREATE TABLE IF NOT EXISTS asset_status_history (
            id INTEGER NOT NULL AUTO_INCREMENT,
            asset_id INTEGER NOT NULL,
            previous_status VARCHAR(50) NULL,
            new_status VARCHAR(50) NOT NULL,
            changed_by VARCHAR(36) NOT NULL,
            change_reason VARCHAR(255) NULL,
            workflow_module VARCHAR(100) NULL,
            session_id VARCHAR(100) NULL,
            notes TEXT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_asset_id (asset_id),
            INDEX idx_changed_by (changed_by),
            INDEX idx_workflow_module (workflow_module),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
    }
    
    try:
        with engine.connect() as conn:
            for table_name, sql in tables_sql.items():
                try:
                    logger.info(f"Creating table: {table_name}")
                    conn.execute(text(sql))
                    conn.commit()
                    logger.info(f"✅ Successfully created table: {table_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to create table {table_name}: {e}")
                    
        logger.info("🎉 Workflow tables creation completed!")
        
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
        return False
        
    return True

if __name__ == "__main__":
    print("🔧 Creating missing workflow tables...")
    success = create_missing_tables()
    if success:
        print("✅ All workflow tables created successfully!")
    else:
        print("❌ Some tables failed to create. Check logs above.")
        sys.exit(1)
