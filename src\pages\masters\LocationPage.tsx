import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Search, Plus, FileDown, Printer, Edit, Trash, CheckCircle, XCircle, Copy, MapPin } from "lucide-react";
import locationService, { Location, LocationCreationData } from "@/services/locationService";
import { exportData } from "@/utils/exportUtils";

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 5000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`flex items-center p-4 mb-3 rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out ${
            toast.type === 'success' 
              ? 'bg-green-50 border-green-500 text-green-800' 
              : 'bg-red-50 border-red-500 text-red-800'
          }`}
        >
          <span className="flex-1 text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className={`ml-3 p-1 rounded-full text-lg font-bold ${
              toast.type === 'success' ? 'hover:bg-green-200' : 'hover:bg-red-200'
            }`}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// API service
const API_BASE_URL = 'http://localhost:8000/api';

const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
    });

    if (!response.ok) {
      // Handle specific HTTP status codes
      if (response.status === 404) {
        throw new Error(`Endpoint not found: ${endpoint}`);
      } else if (response.status === 500) {
        throw new Error('Server error - please try again later');
      } else if (response.status === 307) {
        // Handle redirect - the API might be redirecting from /locations to /locations/
        throw new Error('API endpoint redirect detected');
      }
      
      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  } catch (error) {
    // Log the full error for debugging
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};

// Remove duplicate interfaces since they're imported from the service

// States data
const states = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  // Add more states as needed
];

export default function LocationPage() {
  console.log("🔍 LocationPage: Component is rendering");

  // State management
  const [locations, setLocations] = useState<Location[]>([]);
  const [allLocations, setAllLocations] = useState<Location[]>([]); // Store all locations for counting
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string | null>(null);
  const [selectedTypeFilter, setSelectedTypeFilter] = useState<string | null>(null);
  const [uniqueTypes, setUniqueTypes] = useState<string[]>([]);
  const [formData, setFormData] = useState<LocationCreationData>({
    name: "",
    area: "",
    address: "",
    addressLine2: "",
    city: "",
    state: "",
    zip: "",
    status: true,
    type: ""
  });

  // Load asset types from the API
  const loadAssetTypes = async () => {
    try {
      console.log("🏷️ Loading asset types...");
      const response = await apiCall('/masters/locations/asset-types');
      if (response.success && Array.isArray(response.data.assetTypes)) {
        setUniqueTypes(response.data.assetTypes);
        console.log("✅ Loaded asset types:", response.data.assetTypes);
      }
    } catch (error) {
      console.error("❌ Error loading asset types:", error);
      // Fallback: don't break the page if asset types endpoint doesn't exist
      console.log("📝 Asset types endpoint not available, using types from locations data");
    }
  };

  // Load locations and asset types on component mount
  useEffect(() => {
    console.log("🎯 useEffect triggered");
    loadLocations();
    loadAllTypes();
    // Only load asset types if the endpoint exists, otherwise rely on loadAllTypes
    loadAssetTypes();
  }, []);

  // Load all unique types and store all locations for counting
  const loadAllTypes = async () => {
    try {
      console.log("🏷️ Loading all unique types and locations for counting...");
      const response = await apiCall('/masters/locations/');

      let fetchedAllLocations: Location[] = [];
      if (response.success && Array.isArray(response.data)) {
        fetchedAllLocations = response.data.map((loc: any) => ({
          ...loc,
          status: typeof loc.status === 'string' ? loc.status === 'Active' : Boolean(loc.status)
        }));
      }

      // Store all locations for counting purposes
      setAllLocations(fetchedAllLocations);

      // Extract unique types from all locations
      const types = new Set<string>();
      fetchedAllLocations.forEach(loc => {
        if (loc.type) {
          types.add(loc.type);
        }
      });
      setUniqueTypes(Array.from(types).sort());
      console.log("✅ Loaded unique types:", Array.from(types).sort());
      console.log("✅ Stored all locations for counting:", fetchedAllLocations.length);
    } catch (error) {
      console.error("❌ Error loading types:", error);
      toast.error(`Failed to load location data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Load locations from the API with optional filtering
  const loadLocations = async (filters?: { type?: string; source?: string; search?: string }) => {
    try {
      console.log("🚀 Starting loadLocations with filters:", filters);
      setLoading(true);
      setError(null);

      // Build endpoint with filters - use trailing slash to match backend route
      let endpoint = '/masters/locations/';
      if (filters) {
        const params = new URLSearchParams();
        if (filters.type && filters.type !== 'all') params.append('type', filters.type);
        if (filters.source) params.append('source', filters.source);
        if (filters.search) params.append('search', filters.search);
        if (params.toString()) {
          endpoint += `?${params.toString()}`;
        }
      }

      const loadResponse = await apiCall(endpoint);
      console.log("📡 API Response:", loadResponse);

      let fetchedLocations: Location[] = [];
      if (loadResponse.success && Array.isArray(loadResponse.data)) {
        fetchedLocations = loadResponse.data.map((loc: any) => ({
          ...loc,
          status: typeof loc.status === 'string' ? loc.status === 'Active' : Boolean(loc.status)
        }));
      } else {
        throw new Error('Invalid response format from server');
      }

      setLocations(fetchedLocations);

    } catch (err) {
      console.error('❌ Error loading locations:', err);
      setError(err instanceof Error ? err.message : 'Failed to load locations');
      setLocations([]);
      toast.error(`Failed to load locations: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
      console.log("🏁 Loading finished");
    }
  };

  // Handle adding a new location
  const handleAddLocation = async () => {
    try {
      console.log("➕ Adding location:", formData);
      setLoading(true);

      const addResponse = await apiCall('/masters/locations/', {
        method: 'POST',
        body: JSON.stringify(formData),
      });

      if (addResponse.success) {
        toast.success('Location created successfully!');
        setShowAddDialog(false);
        // Reset form data
        setFormData({
          name: "",
          area: "",
          address: "",
          addressLine2: "",
          city: "",
          state: "",
          zip: "",
          status: true,
          type: ""
        });
        loadLocations();
        loadAllTypes(); // Refresh the types and counts
      } else {
        toast.error(`Error: ${addResponse.message}`);
      }
    } catch (err) {
      console.error('Error adding location:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle editing a location
  const handleEditLocation = async () => {
    if (!currentLocation) return;

    try {
      console.log("✏️ Editing location:", currentLocation.id, formData);
      setLoading(true);

      const editResponse = await apiCall(`/masters/locations/${currentLocation.id}`, {
        method: 'PUT',
        body: JSON.stringify(formData),
      });

      if (editResponse.success) {
        toast.success('Location updated successfully!');
        setShowEditDialog(false);
        loadLocations();
        loadAllTypes(); // Refresh the types and counts
      } else {
        toast.error(`Error: ${editResponse.message}`);
      }
    } catch (err) {
      console.error('Error updating location:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete location
  const handleDeleteLocation = async (location: Location) => {
    if (!confirm(`Are you sure you want to delete "${location.name}"?`)) {
      return;
    }

    try {
      console.log("🗑️ Deleting location:", location.id);
      setLoading(true);

      const deleteResponse = await apiCall(`/masters/locations/${location.id}`, {
        method: 'DELETE',
      });

      if (deleteResponse.success) {
        toast.success('Location deleted successfully!');
        loadLocations();
        loadAllTypes(); // Refresh the types and counts
      } else {
        toast.error(`Error: ${deleteResponse.message}`);
      }
    } catch (err) {
      console.error('Error deleting location:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Filter locations based on search query and selected filter
  const filteredLocations = locations.filter(location => {
    // Apply type filter
    if (selectedTypeFilter !== null && location.type !== selectedTypeFilter) {
      return false;
    }

    // Apply search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      return (
        location.name.toLowerCase().includes(searchLower) ||
        (location.area && location.area.toLowerCase().includes(searchLower)) ||
        (location.city && location.city.toLowerCase().includes(searchLower)) ||
        (location.state && location.state.toLowerCase().includes(searchLower))
      );
    }

    return true;
  });

  // Handle input changes for form fields
  const handleInputChange = (field: keyof LocationCreationData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'status' ? Boolean(value) : value
    }));
  };

  // Open edit dialog with location data
  const openEditDialog = (location: Location) => {
    setCurrentLocation(location);
    setFormData({
      name: location.name,
      area: location.area || "",
      address: location.address || "",
      addressLine2: location.addressLine2 || "",
      city: location.city || "",
      state: location.state || "",
      zip: location.zip || "",
      status: typeof location.status === 'string' ? location.status === 'Active' : Boolean(location.status),
      type: location.type || ""
    });
    setShowEditDialog(true);
  };

  // Open view dialog with location data
  const openViewDialog = (location: Location) => {
    setCurrentLocation(location);
    setShowViewDialog(true);
  };

  // Handle exporting data in various formats
  const handleExport = (format: string) => {
    // Prepare data for export with proper column names
    const exportableData = filteredLocations.map(location => ({
      Location: location.name,
      Area: location.area || '',
      City: location.city || '',
      State: location.state || '',
                  Status: location.status,
      Type: location.type || 'general',
      Created: new Date(location.createdAt).toLocaleDateString()
    }));

    // Define column headers in the desired order
    const headers = ['Location', 'Area', 'City', 'State', 'Status', 'Type', 'Created'];

    // Call the export function with the prepared data
    exportData(exportableData, format, 'all_locations', 'All Locations', headers);
  };

  // Handle printing locations with custom formatting and timestamp
  const handlePrint = () => {
    try {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('Please allow popups to print data');
        return;
      }

      // Get current date and time for timestamp
      const now = new Date();
      const formattedDate = now.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
      const formattedTime = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
      const timestamp = `Generated on: ${formattedDate} ${formattedTime}`;

      // Generate HTML content with improved styling and election logo
      const htmlContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Polling Locations</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; }
              .logo { max-height: 50px; }
              h1 { margin: 0; color: #333; font-size: 24px; }
              .timestamp { color: #666; font-size: 12px; margin-top: 5px; }
              table { width: 100%; border-collapse: collapse; margin: 20px 0; }
              th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
              th { background-color: #f5f5f5; font-weight: bold; color: #333; }
              tr:nth-child(odd) { background-color: #f9f9f9; }
              tr:nth-child(even) { background-color: #ffffff; }
              .status-dot {
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 6px;
              }
              .active { background-color: #10b981; }
              .inactive { background-color: #9ca3af; }
              .button-container {
                margin: 20px 0;
                text-align: center;
                display: flex;
                justify-content: center;
                gap: 10px;
              }
              button {
                padding: 8px 20px;
                background-color: #4361ee;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-weight: 500;
                font-size: 14px;
              }
              button:hover { background-color: #3a56d4; }
              @media print {
                .button-container { display: none; }
                body { margin: 0; padding: 15px; }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <div>
                <h1>Polling Locations</h1>
                <div class="timestamp">${timestamp}</div>
              </div>
              <img src="/logo.jpg" alt="Election Logo" class="logo" onerror="this.src='/assets/logo.jpg'; this.onerror=null;" />
            </div>

            <table>
              <thead>
                <tr>
                  <th>Location</th>
                  <th>Area</th>
                  <th>City</th>
                  <th>State</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                ${filteredLocations.map(location => `
                  <tr>
                    <td>${location.name}</td>
                    <td>${location.area}</td>
                    <td>${location.city}</td>
                    <td>${location.state}</td>
                    <td>
                      <div style="display: flex; align-items: center;">
                        <span class="status-dot ${location.status ? 'active' : 'inactive'}"></span>
                        ${location.status ? 'Active' : 'Inactive'}
                      </div>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>

            <div class="button-container">
              <button onclick="window.print()">Print</button>
              <button onclick="window.close()">Close</button>
            </div>

            <script>
              // Auto-focus the print window
              window.focus();

              // Log print events
              window.addEventListener('beforeprint', function() {
                console.log('Printing started');
              });

              window.addEventListener('afterprint', function() {
                console.log('Printing completed');
              });
            </script>
          </body>
        </html>
      `;

      // Write the HTML content to the window
      printWindow.document.open();
      printWindow.document.write(htmlContent);
      printWindow.document.close();

    } catch (error) {
      console.error('Print preparation failed:', error);
      alert('Failed to prepare print view. Please try again.');
    }
  };

  console.log("🎨 About to render with state:", {
    loading,
    error,
    locationsCount: locations.length,
    filteredCount: filteredLocations.length
  });

  // Render loading state
  if (loading) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Location Management</h1>
          </div>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-white bg-blue-500 hover:bg-blue-400 transition ease-in-out duration-150 cursor-not-allowed">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Locations...
              </div>
              <p className="mt-2 text-gray-600">Please wait while we fetch the data.</p>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Render error state
  if (error) {
    return (
      <AppLayout>
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Location Management</h1>
          </div>
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
                <div className="flex items-center mb-4">
                  <svg className="h-6 w-6 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-lg font-semibold text-red-800">Error Loading Locations</h3>
                </div>
                <p className="text-red-700 mb-4">{error}</p>
                <Button onClick={loadLocations} className="bg-blue-500 hover:bg-blue-600">
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Retry
                </Button>
              </div>
            </div>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Render main content
  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 pb-4 border-b">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Location Management</h1>
            <p className="mt-1 text-gray-500">
              Manage polling locations and precincts - {filteredLocations.length} of {allLocations.length} locations
              {error && <span className="text-red-500 ml-2">({error})</span>}
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="mr-2">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => handleExport('pdf')}>
                  PDF Document
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('excel')}>
                  Excel Spreadsheet
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                  CSV File
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('copy')}>
                  Copy to Clipboard
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" className="mr-2" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button size="sm" onClick={() => setShowAddDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          </div>
        </div>

        {/* Dynamic Filter Buttons */}
        <Card className="mb-4">
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-2">
              <Button
                variant={selectedFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  setSelectedFilter('all');
                  loadLocations();
                }}
                className="flex items-center gap-2"
              >
                <MapPin className="h-4 w-4" />
                All Locations ({allLocations.length})
              </Button>

              {/* Dynamic Asset Type Filters */}
              {uniqueTypes.map((assetType, index) => {
                return (
                  <Button
                    key={assetType}
                    variant={selectedFilter === assetType ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      setSelectedFilter(assetType);
                      loadLocations({ type: assetType });
                    }}
                    className="flex items-center gap-2"
                  >
                    {assetType} ({allLocations.filter(l => l.type === assetType).length})
                  </Button>
                );
              })}



              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  loadLocations();
                  loadAllTypes();
                  // Don't call loadAssetTypes here since it might fail
                }}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg font-medium">
                {selectedFilter === null ? 'All Locations' :
                 selectedFilter === 'packing' ? 'Packing Locations' : 'General Locations'}
              </CardTitle>
              <div className="relative w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="search"
                  placeholder="Search locations..."
                  className="pl-8 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table className="border-collapse">
              <TableHeader>
                <TableRow className="border-b border-gray-200">
                  <TableHead className="w-[200px] py-3 text-gray-700">Location</TableHead>
                  <TableHead className="w-[80px] py-3 text-gray-700">Type</TableHead>
                  <TableHead className="w-[100px] py-3 text-gray-700">Area</TableHead>
                  <TableHead className="w-[150px] py-3 text-gray-700">City</TableHead>
                  <TableHead className="w-[80px] py-3 text-gray-700">State</TableHead>
                  <TableHead className="w-[100px] py-3 text-gray-700">Status</TableHead>
                  <TableHead className="text-right py-3 text-gray-700">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLocations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      {searchQuery ? 'No matching locations found' : 'No locations found'}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredLocations.map((location) => (
                    <TableRow key={location.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <TableCell className="font-medium py-4">{location.name}</TableCell>
                      <TableCell className="py-4">
                        <span className="text-xs capitalize">{location.type || 'general'}</span>
                      </TableCell>
                      <TableCell className="py-4">{location.area || '-'}</TableCell>
                      <TableCell className="py-4">{location.city || '-'}</TableCell>
                      <TableCell className="py-4">{location.state || '-'}</TableCell>
                      <TableCell className="py-4">
                        <div className="flex items-center gap-2">
                          {location.status ? (
                            <>
                              <div className="h-4 w-4 rounded-full bg-green-500"></div>
                              <span className="text-gray-700">Active</span>
                            </>
                          ) : (
                            <>
                              <div className="h-4 w-4 rounded-full bg-gray-300"></div>
                              <span className="text-gray-700">Inactive</span>
                            </>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right py-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 mr-2"
                          onClick={() => openViewDialog(location)}
                        >
                          View
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 mr-2"
                          onClick={() => openEditDialog(location)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                          onClick={() => handleDeleteLocation(location)}
                        >
                          <Trash className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Add Location Dialog */}
        <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-xl">Add Location</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Location <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter location name"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Type <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.type || ""}
                  onChange={(e) => handleInputChange("type", e.target.value)}
                  placeholder="Enter location type (e.g., Warehouse, Office)"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Area
                </label>
                <Input
                  value={formData.area || ""}
                  onChange={(e) => handleInputChange("area", e.target.value)}
                  placeholder="Enter area code"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Address Line 1
                </label>
                <Input
                  value={formData.address || ""}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter address line 1"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Address Line 2
                </label>
                <Input
                  value={formData.addressLine2 || ""}
                  onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                  placeholder="Enter address line 2 (optional)"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  City
                </label>
                <Input
                  value={formData.city || ""}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Enter city"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  State
                </label>
                <Select
                  value={formData.state || ""}
                  onValueChange={(value) => handleInputChange("state", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a state" />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  ZIP
                </label>
                <Input
                  value={formData.zip || ""}
                  onChange={(e) => handleInputChange("zip", e.target.value)}
                  placeholder="Enter ZIP code"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Status <span className="text-red-500">*</span>
                </label>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={formData.status}
                    onCheckedChange={(checked) =>
                      handleInputChange("status", checked)
                    }
                  />
                  <span className="text-sm">
                    {formData.status ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddLocation}>
                Save Location
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Location Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-xl">Edit Location</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Location <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter location name"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Type <span className="text-red-500">*</span>
                </label>
                <Input
                  value={formData.type || ""}
                  onChange={(e) => handleInputChange("type", e.target.value)}
                  placeholder="Enter location type (e.g., Warehouse, Office)"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Area
                </label>
                <Input
                  value={formData.area || ""}
                  onChange={(e) => handleInputChange("area", e.target.value)}
                  placeholder="Enter area code"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Address Line 1
                </label>
                <Input
                  value={formData.address || ""}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter address line 1"
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Address Line 2
                </label>
                <Input
                  value={formData.addressLine2 || ""}
                  onChange={(e) => handleInputChange("addressLine2", e.target.value)}
                  placeholder="Enter address line 2 (optional)"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  City
                </label>
                <Input
                  value={formData.city || ""}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Enter city"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  State
                </label>
                <Select
                  value={formData.state || ""}
                  onValueChange={(value) => handleInputChange("state", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a state" />
                  </SelectTrigger>
                  <SelectContent>
                    {states.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  ZIP
                </label>
                <Input
                  value={formData.zip || ""}
                  onChange={(e) => handleInputChange("zip", e.target.value)}
                  placeholder="Enter ZIP code"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Status <span className="text-red-500">*</span>
                </label>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={formData.status}
                    onCheckedChange={(checked) =>
                      handleInputChange("status", checked)
                    }
                  />
                  <span className="text-sm">
                    {formData.status ? "Active" : "Inactive"}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditLocation}>
                Update Location
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* View Location Dialog */}
        <Dialog open={showViewDialog} onOpenChange={setShowViewDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-xl">Location Details</DialogTitle>
            </DialogHeader>
            {currentLocation && (
              <div className="space-y-4 py-4">
                <div className="grid grid-cols-[120px_1fr] bg-gray-50 p-4 rounded-md">
                  <div className="font-medium text-gray-600">Location</div>
                  <div>{currentLocation.name}</div>
                </div>

                <div className="grid grid-cols-[120px_1fr] bg-gray-50 p-4 rounded-md">
                  <div className="font-medium text-gray-600">Type</div>
                  <div className="flex items-center gap-2">
                    <span className="capitalize">{currentLocation.type || 'general'}</span>
                  </div>
                </div>

                {(currentLocation.area || currentLocation.address || currentLocation.city) && (
                  <div className="grid grid-cols-[120px_1fr] bg-gray-50 p-4 rounded-md">
                    <div className="font-medium text-gray-600">Details</div>
                    <div className="space-y-1">
                      {currentLocation.area && <div><strong>Area:</strong> {currentLocation.area}</div>}
                      {currentLocation.address && <div><strong>Address:</strong> {currentLocation.address}</div>}
                      {currentLocation.addressLine2 && <div>{currentLocation.addressLine2}</div>}
                      {(currentLocation.city || currentLocation.state || currentLocation.zip) && (
                        <div>
                          <strong>Location:</strong> {[currentLocation.city, currentLocation.state, currentLocation.zip].filter(Boolean).join(', ')}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-[120px_1fr] bg-gray-50 p-4 rounded-md">
                  <div className="font-medium text-gray-600">Status</div>
                  <div className="flex items-center gap-2">
                    {currentLocation.status ? (
                      <>
                        <div className="h-4 w-4 rounded-full bg-green-500"></div>
                        <span className="text-green-600 font-medium">Active</span>
                      </>
                    ) : (
                      <>
                        <div className="h-4 w-4 rounded-full bg-gray-300"></div>
                        <span className="text-gray-600 font-medium">Inactive</span>
                      </>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-[120px_1fr] bg-gray-50 p-4 rounded-md">
                  <div className="font-medium text-gray-600">Created</div>
                  <div>{new Date(currentLocation.createdAt).toLocaleDateString()}</div>
                </div>
              </div>
            )}
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowViewDialog(false)}>
                Close
              </Button>
              {currentLocation && (
                <Button onClick={() => {
                  setShowViewDialog(false);
                  openEditDialog(currentLocation);
                }}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Toast Container */}
        <ToastContainer />
      </div>
    </AppLayout>
  );
}
