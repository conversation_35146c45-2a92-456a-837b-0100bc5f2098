
import { assetService } from "@/services/assetService";
import { workflowService } from "@/services/workflowService";
import { AppLayout } from "@/components/layout/AppLayout";
import { StatCard } from "@/components/dashboard/StatCard";
import { AssetStatusCard } from "@/components/dashboard/AssetStatusCard";
import { MaintenanceCard } from "@/components/dashboard/MaintenanceCard";
import { AccessLevelBanner } from "@/components/dashboard/AccessLevelBanner";
import { DashboardFilters } from "@/components/dashboard/DashboardFilters";
import { CountyStatsPanel } from "@/components/dashboard/CountyStatsPanel";
import { CountyStats } from "@/components/dashboard/CountyStatsPopup";
import { countyStatsData } from "@/data/county-stats-generator";
import SouthCarolinaMap from "@/components/map/SouthCarolinaMap";
import { EnhancedStatewideOverview } from "@/components/dashboard/EnhancedStatewideOverview";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/App";
import { useState, useEffect, useMemo } from "react";
import {
  Archive,
  CalendarCheck,
  Map,
  BarChart,
  ListChecks,
  Package,
  FileText,
  Settings,
  Users,
  Truck,
  ShoppingCart,
  CheckCircle2,
  Clock,
  ArrowRight,
  PieChart,
  BarChart2,
  Activity,
  TrendingUp,
  AlertTriangle,
  Target,
  Calendar,
  BarChart3
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { AssetAcquisitionChart } from "@/components/reports/AssetAcquisitionChart";
import { BarChart as RechartsBarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";

const Dashboard = () => {
  const { userCounty, accessLevel } = useAuth();

  // Initialize filter states
  const [selectedCounty, setSelectedCounty] = useState<string>(accessLevel === 'county' ? userCounty || 'all' : 'all');
  const [selectedEquipmentType, setSelectedEquipmentType] = useState<string>('all');

  // County statistics panel state
  const [selectedCountyStats, setSelectedCountyStats] = useState<CountyStats | null>(null);

   // Dashboard tab state
  const [activeTab, setActiveTab] = useState<string>('main');
  const [dashboardView, setDashboardView] = useState<'main' | 'statewide'>('main');

  // Backend data states
  const [assets, setAssets] = useState<any[]>([]);
  const [assetStats, setAssetStats] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load dashboard data from backend
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Build filters based on user access level and selections
        const filters: any = {};

        if (accessLevel === 'county' && userCounty) {
          filters.county = userCounty;
        } else if (selectedCounty !== 'all') {
          filters.county = selectedCounty;
        }

        if (selectedEquipmentType !== 'all') {
          filters.category = selectedEquipmentType;
        }

        // Fetch assets and statistics
        const [assetsResponse, statsResponse] = await Promise.all([
          assetService.getAssets(filters),
          assetService.getAssetStats()
        ]);

        if (assetsResponse.success) {
          setAssets(assetsResponse.data || []);
        }

        if (statsResponse.success) {
          setAssetStats(statsResponse.data || {});
        }

      } catch (err) {
        console.error('Dashboard data loading error:', err);
        setError('Failed to load dashboard data');
        // Fallback to empty data instead of crashing
        setAssets([]);
        setAssetStats({});
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [selectedCounty, selectedEquipmentType, accessLevel, userCounty]);

  // Calculate statistics from loaded data
  const totalAssets = assets.length;
  const operationalAssets = assets.filter(asset =>
    asset.status === 'Ready' || asset.status === 'Available' || asset.status === 'Operational'
  ).length;
  const operationalPercentage = totalAssets > 0 ? Math.round((operationalAssets / totalAssets) * 100) : 0;

  const maintenanceCount = assets.filter(asset =>
    asset.status === 'Under Maintenance' || asset.status === 'Maintenance'
  ).length;
  const repairCount = assets.filter(asset =>
    asset.status === 'Damaged' || asset.status === 'Repair'
  ).length;

  // Handle filter changes
  const handleFilterChange = (filters: { county: string; equipmentType: string }) => {
    setSelectedCounty(filters.county);
    setSelectedEquipmentType(filters.equipmentType);
  };

  // Handle county click for statistics panel
  const handleCountyClick = (countyName: string) => {
    // TODO: Fetch real county statistics from backend
    const stats = countyStatsData[countyName];
    if (stats) {
      setSelectedCountyStats(stats);
    }
  };

  // Handle closing the county stats panel
  const handleCloseCountyStats = () => {
    setSelectedCountyStats(null);
  };

  // Calculate L&A testing stats from real data
  const [laStats, setLaStats] = useState({ total: 0, completed: 0, percentage: 0 });
  const [elections, setElections] = useState<any[]>([]);

  useEffect(() => {
    const loadLAStats = async () => {
      try {
        // Fetch L&A checklist statistics
        const laStatsResponse = await workflowService.getWorkflowStats(
          undefined, // start_date
          undefined, // end_date
          'LA_CHECKLIST' // module
        );

        if (laStatsResponse.success) {
          const data = laStatsResponse.data;
          setLaStats({
            total: data.total_sessions || 0,
            completed: data.completed_sessions || 0,
            percentage: data.completion_percentage || 0
          });
        }

        // Fetch upcoming elections
        // TODO: Replace with actual elections API call
        setElections([
          { name: "Primary Election", date: "June 15, 2025", daysRemaining: 45, readinessPercentage: 78 },
          { name: "General Election", date: "November 3, 2025", daysRemaining: 186, readinessPercentage: 32 }
        ]);

      } catch (err) {
        console.error('Error loading L&A stats:', err);
        // Use fallback values
        setLaStats({ total: 0, completed: 0, percentage: 0 });
        setElections([]);
      }
    };

    loadLAStats();
  }, [selectedCounty, accessLevel, userCounty]);

  // Asset distribution by county (calculated from real data)
  const assetDistributionByCounty = useMemo(() => {
    if (assets.length === 0) return [];

    // If county-level access or specific county selected, show only that county
    if ((accessLevel === 'county' && userCounty) || (selectedCounty !== 'all')) {
      const countyName = accessLevel === 'county' ? userCounty : selectedCounty;
      return [{
        county: countyName || 'Unknown',
        percentage: 100
      }];
    }

    // Otherwise, calculate distribution based on filtered assets
    const countyAssetCounts: Record<string, number> = {};
    let totalCount = 0;

    // Count assets by county, applying equipment type filter
    assets.forEach(asset => {
      if (selectedEquipmentType === 'all' || asset.type === selectedEquipmentType) {
        const county = asset.county || 'Unknown';
        countyAssetCounts[county] = (countyAssetCounts[county] || 0) + 1;
        totalCount++;
      }
    });

    if (totalCount === 0) return [];

    // Convert to percentage and format for display
    const distribution = Object.entries(countyAssetCounts)
      .map(([county, count]) => ({
        county,
        percentage: Math.round((count / totalCount) * 100)
      }))
      .sort((a, b) => b.percentage - a.percentage);

    // Take top 5 counties and group the rest as "Other"
    if (distribution.length > 5) {
      const top5 = distribution.slice(0, 5);
      const otherPercentage = distribution.slice(5).reduce((sum, item) => sum + item.percentage, 0);

      return [
        ...top5,
        { county: "Other", percentage: otherPercentage }
      ];
    }

    return distribution;
  }, [assets, selectedEquipmentType, accessLevel, userCounty, selectedCounty]);

  // Backend data for charts and statistics
  const [monthlyAssetActivity, setMonthlyAssetActivity] = useState<any[]>([]);
  const [consumablesInventory, setConsumablesInventory] = useState<any[]>([]);

  useEffect(() => {
    const loadChartData = async () => {
      try {
        // TODO: Replace with actual API calls when available
        // For now, using calculated data from assets

        // Calculate monthly activity from asset status history
        // This would ideally come from asset status history API
        setMonthlyAssetActivity([
          { month: "Jan", checkedOut: 0, checkedIn: 0, maintenance: 0 },
          { month: "Feb", checkedOut: 0, checkedIn: 0, maintenance: 0 },
          { month: "Mar", checkedOut: 0, checkedIn: 0, maintenance: 0 },
          { month: "Apr", checkedOut: 0, checkedIn: 0, maintenance: 0 },
          { month: "May", checkedOut: 0, checkedIn: 0, maintenance: 0 },
          { month: "Jun", checkedOut: 0, checkedIn: 0, maintenance: 0 }
        ]);

        // TODO: Fetch consumables inventory from backend
        setConsumablesInventory([]);

      } catch (err) {
        console.error('Error loading chart data:', err);
        setMonthlyAssetActivity([]);
        setConsumablesInventory([]);
      }
    };

    loadChartData();
  }, []);

  // Asset status distribution based on real data
  const assetStatusDistribution = useMemo(() => {
    const statusCounts: Record<string, number> = {};

    assets.forEach(asset => {
      const status = asset.status || 'Unknown';
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });

    // Map to standardized status categories with colors
    const statusMapping: Record<string, { label: string; color: string }> = {
      'Ready': { label: 'Operational', color: '#4ade80' },
      'Available': { label: 'Operational', color: '#4ade80' },
      'Operational': { label: 'Operational', color: '#4ade80' },
      'New': { label: 'New', color: '#3b82f6' },
      'Under Maintenance': { label: 'Maintenance', color: '#facc15' },
      'Maintenance': { label: 'Maintenance', color: '#facc15' },
      'Damaged': { label: 'Repair', color: '#f87171' },
      'Repair': { label: 'Repair', color: '#f87171' },
      'Failed': { label: 'Repair', color: '#f87171' },
      'Retired': { label: 'Retired', color: '#94a3b8' },
      'Decommissioned': { label: 'Retired', color: '#94a3b8' }
    };

    const distribution: Record<string, { count: number; color: string }> = {};

    Object.entries(statusCounts).forEach(([status, count]) => {
      const mapping = statusMapping[status] || { label: 'Other', color: '#6b7280' };
      if (!distribution[mapping.label]) {
        distribution[mapping.label] = { count: 0, color: mapping.color };
      }
      distribution[mapping.label].count += count;
    });

    return Object.entries(distribution).map(([status, data]) => ({
      status,
      count: data.count,
      color: data.color
    }));
  }, [assets]);

  // Asset usage statistics (mock data)
  const assetUsageStats = [
    { day: "Mon", count: 42 },
    { day: "Tue", count: 56 },
    { day: "Wed", count: 78 },
    { day: "Thu", count: 63 },
    { day: "Fri", count: 85 },
    { day: "Sat", count: 47 },
    { day: "Sun", count: 25 }
  ];

  // Asset acquisition timeline (mock data)
  const assetAcquisitionTimeline = [
    { year: "2020", count: 120 },
    { year: "2021", count: 85 },
    { year: "2022", count: 95 },
    { year: "2023", count: 110 },
    { year: "2024", count: 50 }
  ];

  // Top issues reported (mock data)
  const topIssuesReported = [
    { issue: "Battery Failure", count: 24, percentage: 32 },
    { issue: "Screen Malfunction", count: 18, percentage: 24 },
    { issue: "Software Error", count: 15, percentage: 20 },
    { issue: "Printer Jam", count: 12, percentage: 16 },
    { issue: "Other", count: 6, percentage: 8 }
  ];

  // Asset allocation by purpose (mock data)
  const assetAllocationByPurpose = [
    { purpose: "Voting", percentage: 65, color: "#3b82f6" },
    { purpose: "Training", percentage: 15, color: "#8b5cf6" },
    { purpose: "Testing", percentage: 12, color: "#ec4899" },
    { purpose: "Backup", percentage: 8, color: "#f97316" }
  ];
  // Helper function to determine region
  const getRegion = (countyName: string): 'Upstate' | 'Midlands' | 'Lowcountry' => {
    const upstateCounties = ['Greenville', 'Spartanburg', 'Anderson', 'Pickens', 'Cherokee', 'York', 'Oconee', 'Laurens', 'Union', 'Chester', 'Lancaster'];
    const midlandsCounties = ['Richland', 'Lexington', 'Aiken', 'Kershaw', 'Newberry', 'Fairfield', 'Saluda', 'Edgefield', 'McCormick'];
    const lowcountryCounties = ['Charleston', 'Berkeley', 'Dorchester', 'Beaufort', 'Horry', 'Georgetown', 'Colleton', 'Jasper', 'Hampton'];
    
    if (upstateCounties.includes(countyName)) return 'Upstate';
    if (midlandsCounties.includes(countyName)) return 'Midlands';
    return 'Lowcountry'; // Default to Lowcountry for any remaining counties
  };

  // Convert countyStatsData to array format for EnhancedStatewideOverview
  const statewideData = Object.entries(countyStatsData).map(([countyName, stats]) => ({
    county: countyName,
    assetCount: stats.totalAssets,    utilizationRate: stats.readinessScore,
    maintenanceRate: (stats.maintenanceCases / stats.totalAssets) * 100,
    operational: Math.round(stats.totalAssets * (stats.operationalPercentage / 100)),
    maintenance: stats.maintenanceCases,
    repair: Math.round(stats.totalAssets * 0.1),
    decommissioned: Math.round(stats.totalAssets * 0.05),
    pollbooks: stats.assetBreakdown.pollbooks,
    scanners: stats.assetBreakdown.scanners,
    bmds: stats.assetBreakdown.bmds,
    criticalAlerts: 0,
    lastUpdated: stats.lastUpdated,
    region: getRegion(countyName),
    population: 100000 + Math.floor(Math.random() * 400000), // Random population between 100k and 500k
    assetsPerCapita: stats.totalAssets / (100000 + Math.floor(Math.random() * 400000))
  }));

  // Update handleFilterChange to match expected signature
  const handleEnhancedFilterChange = (type: string, value: string) => {
    if (type === 'county') {
      setSelectedCounty(value);
    } else if (type === 'assetType') {
      setSelectedEquipmentType(value);
    }
  };

  // Transform AssetSummary[] to StatewideAssetData[]
  const transformedStatewideData = useMemo(() => {
    return assetSummaries.map((summary, index) => ({
      county: counties[index % counties.length].name,
      assetCount: summary.total,
      utilizationRate: Math.round((summary.operational / summary.total) * 100),
      maintenanceRate: Math.round((summary.maintenance / summary.total) * 100),
      operational: summary.operational,
      maintenance: summary.maintenance,
      repair: summary.repair,
      decommissioned: summary.decommissioned,
      pollbooks: summary.type === 'pollbook' ? summary.total : 0,
      scanners: summary.type === 'scanner' ? summary.total : 0,
      bmds: summary.type === 'bmd' ? summary.total : 0,
      criticalAlerts: Math.round(Math.random() * 3), // Simulated data
      lastUpdated: new Date().toISOString(),
      region: getRegion(counties[index % counties.length].name),
      population: 100000 + Math.round(Math.random() * 900000), // Simulated population data
      assetsPerCapita: 0.001 + Math.random() * 0.009 // Simulated per capita data
    }));
  }, [assetSummaries, counties]);

  


  return (
    <AppLayout>
      {/* Interactive South Carolina County Map - Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>South Carolina County Map</CardTitle>
            <Map className="h-4 w-4 text-muted-foreground" />
          </div>
          <CardDescription>Click on any county to view detailed statistics</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="h-[400px]">
            <SouthCarolinaMap
              selectedCounty={selectedCounty}
              onCountyClick={handleCountyClick}
              countyStats={countyStatsData}
            />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-6 animate-fade-in p-4">
        {/* County Statistics Panel */}
        {selectedCountyStats && (
          <CountyStatsPanel
            countyStats={selectedCountyStats}
            onClose={handleCloseCountyStats}
          />
        )}
        {/* Dashboard Controls */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <div className="mt-1 flex items-center gap-4">
              <Button
                variant={dashboardView === 'main' ? 'default' : 'outline'}
                onClick={() => setDashboardView('main')}
                className="flex items-center gap-2"
              >
                <Target className="h-4 w-4" />
                Main Dashboard
              </Button>
              <Button
                variant={dashboardView === 'statewide' ? 'default' : 'outline'}
                onClick={() => setDashboardView('statewide')}
                className="flex items-center gap-2"
              >
                <BarChart3 className="h-4 w-4" />
                Statewide Dashboard
              </Button>
              <Link to="/reports">
                <Button variant="outline" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Generate Report
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading dashboard data...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}

        {/* Dashboard Content - Only show when not loading */}
        {!loading && (
          <>

        {/* <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-zenith-blue-dark mb-2">
              {accessLevel === 'county' && userCounty
                ? `${counties.find(c => c.id === userCounty)?.name || userCounty} County Election Equipment Management`
                : selectedCounty !== 'all'
                  ? `${counties.find(c => c.id === selectedCounty)?.name || selectedCounty} County Election Equipment Management`
                  : 'Asset Management Dashboard'}
            </h1>
            <p className="text-muted-foreground">
              {accessLevel === 'county' && userCounty
                ? `Monitor and manage election equipment for ${counties.find(c => c.id === userCounty)?.name || userCounty} County`
                : selectedCounty !== 'all'
                  ? `Monitor and manage election equipment for ${counties.find(c => c.id === selectedCounty)?.name || selectedCounty} County`
                  : 'Statewide Election Equipment Management for South Carolina'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Dashboard Settings
            </Button>
          </div>
        </div> */}

        {/* Dashboard Filters */}
        <DashboardFilters
          counties={counties}
          selectedCounty={selectedCounty}
          selectedEquipmentType={selectedEquipmentType}
          onFilterChange={handleFilterChange}
          accessLevel={accessLevel}
          userCounty={userCounty}
        />

        {/* Access Level Banner */}
        <AccessLevelBanner accessLevel={accessLevel} userCounty={userCounty} />
        {/* Dashboard Content */}
        {dashboardView === 'main' ? (
          <div className="space-y-6">

            {/* Quick Access Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Link to="/masters/assets" className="no-underline">
                <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-2">
                      <Archive className="h-5 w-5 text-blue-600" />
                    </div>
                    <CardTitle className="text-base">Assets</CardTitle>
                    <CardDescription>Manage equipment inventory</CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" size="sm" className="w-full justify-between">
                      View Assets <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              </Link>

              <Link to="/la-checklist" className="no-underline">
                <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-2">
                      <ListChecks className="h-5 w-5 text-green-600" />
                    </div>
                    <CardTitle className="text-base">L&A Checklist</CardTitle>
                    <CardDescription>Logic & Accuracy testing</CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" size="sm" className="w-full justify-between">
                      View Checklists <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              </Link>

              <Link to="/masters/consumables" className="no-underline">
                <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                      <Package className="h-5 w-5 text-purple-600" />
                    </div>
                    <CardTitle className="text-base">Consumables</CardTitle>
                    <CardDescription>Manage election supplies</CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" size="sm" className="w-full justify-between">
                      View Consumables <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              </Link>

              <Link to="/reports" className="no-underline">
                <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                  <CardHeader className="pb-2">
                    <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mb-2">
                      <BarChart className="h-5 w-5 text-amber-600" />
                    </div>
                    <CardTitle className="text-base">Reports</CardTitle>
                    <CardDescription>Analytics and insights</CardDescription>
                  </CardHeader>
                  <CardFooter className="pt-0">
                    <Button variant="ghost" size="sm" className="w-full justify-between">
                      View Reports <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardFooter>
                </Card>
              </Link>
            </div>

            {/* Stats Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="Total Assets"
                value={totalAssets.toLocaleString()}
                icon={<Archive className="h-4 w-4" />}
                description="Election equipment inventory"
              />
              <StatCard
                title="Operational Status"
                value={`${operationalPercentage}%`}
                description={`${operationalAssets.toLocaleString()} of ${totalAssets.toLocaleString()} assets`}
                trend={{ value: 2, positive: true }}
              />
              <StatCard
                title="Maintenance Required"
                value={maintenanceCount}
                icon={<CalendarCheck className="h-4 w-4" />}
                description="Scheduled for service"
                className="text-zenith-yellow"
              />
              <StatCard
                title="Repairs Needed"
                value={repairCount}
                description="Items requiring attention"
                className="text-zenith-red"
              />
            </div>

            {/* Asset Distribution by County and Upcoming Elections */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Asset Distribution by County</CardTitle>
                    <Map className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Geographic distribution of election equipment</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-5">
                    {assetDistributionByCounty.map((item, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">{item.county}</span>
                          <span className="text-sm font-medium">{item.percentage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-blue-600 h-2.5 rounded-full"
                            style={{ width: `${item.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 flex justify-between">
                    <Link to="/map">
                      <Button variant="outline" size="sm">
                        <Map className="h-4 w-4 mr-1" />
                        View Interactive Map
                      </Button>
                    </Link>
                    <Link to="/masters/location">
                      <Button variant="ghost" size="sm">
                        View All Counties <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Upcoming Elections</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Election readiness status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {elections.map((election, index) => (
                      <div key={index} className="border rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <div>
                            <h3 className="font-medium">{election.name}</h3>
                            <p className="text-sm text-muted-foreground">{election.date}</p>
                          </div>
                          <div className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                            {election.daysRemaining} days
                          </div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span>Readiness</span>
                            <span>{election.readinessPercentage}%</span>
                          </div>
                          <Progress value={election.readinessPercentage} className="h-1.5" />
                        </div>
                      </div>
                    ))}
                    <div className="text-right">
                      <Link to="/masters/elections">
                        <Button variant="ghost" size="sm">
                          View All Elections <ArrowRight className="h-4 w-4 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>



            {/* Data Visualization Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Asset Status Distribution Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Asset Status Distribution</CardTitle>
                    <PieChart className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Overview of asset status across all equipment</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-64">
                    {/* Pie Chart Visualization - Fixed to prevent overlapping */}
                    <div className="relative w-48 h-48">
                      {/* Create a simple pie chart with SVG */}
                      <svg viewBox="0 0 100 100" className="w-full h-full">
                        {/* Calculate the pie segments */}
                        {(() => {
                          let startAngle = 0;
                          const total = assetStatusDistribution.reduce((sum, item) => sum + item.count, 0);

                          return assetStatusDistribution.map((item, index) => {
                            const percentage = (item.count / total) * 100;
                            const angle = (percentage / 100) * 360;
                            const endAngle = startAngle + angle;

                            // Calculate the SVG arc path
                            const x1 = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
                            const y1 = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
                            const x2 = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
                            const y2 = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);

                            const largeArcFlag = angle > 180 ? 1 : 0;

                            const pathData = `M 50 50 L ${x1} ${y1} A 40 40 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

                            const path = (
                              <path
                                key={index}
                                d={pathData}
                                fill={item.color}
                                stroke="#fff"
                                strokeWidth="1"
                              />
                            );

                            startAngle = endAngle;
                            return path;
                          });
                        })()}

                        {/* Add a white circle in the center for the text */}
                        <circle cx="50" cy="50" r="20" fill="white" />
                      </svg>

                      {/* Center text with better positioning */}
                      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                        <div className="text-center bg-white bg-opacity-70 rounded-full p-2">
                          <div className="text-2xl font-bold">{totalAssets}</div>
                          <div className="text-xs text-muted-foreground">Total Assets</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 mt-4">
                    {assetStatusDistribution.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
                        <div className="text-sm">{item.status}</div>
                        <div className="ml-auto text-sm font-medium">{item.count}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Monthly Asset Activity Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Monthly Asset Activity</CardTitle>
                    <BarChart2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Asset movement and maintenance trends</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    {/* Bar Chart Visualization - Fixed to prevent overlapping */}
                    <div className="h-full flex items-end">
                      {monthlyAssetActivity.map((month, index) => (
                        <div key={index} className="flex-1 flex flex-col items-center">
                          <div className="w-full flex flex-col items-center">
                            {/* Calculate heights based on maximum values to prevent overflow */}
                            {(() => {
                              const maxValue = Math.max(...monthlyAssetActivity.flatMap(m => [m.checkedOut, m.checkedIn, m.maintenance]));
                              const scale = 150 / maxValue; // Scale to fit within 150px height

                              return (
                                <>
                                  {/* Stacked bar chart with fixed heights */}
                                  <div className="relative w-4/5 mb-1">
                                    <div
                                      className="absolute bottom-0 w-full bg-blue-500 rounded-t"
                                      style={{ height: `${month.checkedOut * scale}px` }}
                                    ></div>
                                  </div>
                                  <div className="relative w-4/5 mb-1">
                                    <div
                                      className="absolute bottom-0 w-full bg-green-500 rounded-t"
                                      style={{ height: `${month.checkedIn * scale}px` }}
                                    ></div>
                                  </div>
                                  <div className="relative w-4/5 mb-1">
                                    <div
                                      className="absolute bottom-0 w-full bg-amber-500 rounded-t"
                                      style={{ height: `${month.maintenance * scale}px` }}
                                    ></div>
                                  </div>
                                </>
                              );
                            })()}
                          </div>
                          <div className="text-xs mt-2">{month.month}</div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-center mt-4 space-x-4">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-xs">Checked Out</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span className="text-xs">Checked In</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                      <span className="text-xs">Maintenance</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Consumables Inventory Status */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Consumables Inventory Status</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </div>
                <CardDescription>Current stock levels and reorder points</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {consumablesInventory.map((item, index) => (
                    <div key={index}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">{item.name}</span>
                        <span className="text-sm">{item.inStock}% in stock</span>
                      </div>
                      <div className="relative">
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                          <div
                            className={`h-2.5 rounded-full ${item.inStock <= item.reorderPoint ? 'bg-red-500' : 'bg-green-500'}`}
                            style={{ width: `${item.inStock}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-right">
                          {item.inStock <= item.reorderPoint ? (
                            <span className="text-red-500 font-medium">Reorder needed</span>
                          ) : (
                            <span className="text-green-500 font-medium">Stock sufficient</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 text-right">
                  <Link to="/masters/consumables">
                    <Button variant="ghost" size="sm">
                      View Inventory <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Assets Status Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {allAssetSummaries
                .filter(summary => selectedEquipmentType === 'all' || summary.type === selectedEquipmentType)
                .map((summary) => (
                  <AssetStatusCard key={summary.type} data={summary} />
                ))}
            </div>

            {/* L&A Testing Progress and Maintenance */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>L&A Testing Progress</CardTitle>
                    <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Logic & Accuracy testing completion</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                      <span className="text-xl font-bold text-blue-600">{laStats.percentage}%</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Progress</span>
                        <span>{laStats.completed} of {laStats.total} sessions</span>
                      </div>
                      <Progress value={laStats.percentage} className="h-2" />
                      <div className="grid grid-cols-3 mt-4 text-center text-sm">
                        <div>
                          <div className="font-medium text-green-600">210</div>
                          <div className="text-xs text-muted-foreground">BMDs</div>
                        </div>
                        <div>
                          <div className="font-medium text-amber-600">45</div>
                          <div className="text-xs text-muted-foreground">Scanners</div>
                        </div>
                        <div>
                          <div className="font-medium text-blue-600">60</div>
                          <div className="text-xs text-muted-foreground">PollPads</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 text-right">
                    <Link to="/la-checklist">
                      <Button variant="ghost" size="sm">
                        View L&A Testing <ArrowRight className="h-4 w-4 ml-1" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              <div className="space-y-4">
                <MaintenanceCard assets={filteredAssets} />
                <div className="text-right">
                  <Link to="/masters/assets">
                    <Button variant="ghost" size="sm">
                      View All Maintenance <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* Asset Usage Statistics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Weekly Asset Usage Chart */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Weekly Asset Usage</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Daily asset checkout activity</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsBarChart
                        data={assetUsageStats}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.9)',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                        />
                        <Legend />
                        <Bar dataKey="count" fill="#3E92CC" name="Checkouts" />
                      </RechartsBarChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="mt-4 text-center text-sm text-muted-foreground">
                    <p>Peak usage on Friday with 85 checkouts</p>
                  </div>
                </CardContent>
              </Card>

              {/* Asset Acquisition Timeline */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Asset Acquisition Timeline</CardTitle>
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Equipment purchases by year</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <AssetAcquisitionChart />
                  </div>
                  <div className="mt-4 text-center text-sm text-muted-foreground">
                    <p>Total of 460 assets acquired since 2020</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Issues and Asset Allocation */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Issues Reported */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Top Issues Reported</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>Most common equipment problems</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {topIssuesReported.map((issue, index) => (
                      <div key={index}>
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-sm font-medium">{issue.issue}</span>
                          <span className="text-sm">{issue.count} reports ({issue.percentage}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-red-500 h-2.5 rounded-full"
                            style={{ width: `${issue.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="mt-6 text-center">
                    <Link to="/reports/issues">
                      <Button variant="outline" size="sm">
                        View Full Issue Report
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>

              {/* Asset Allocation by Purpose */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Asset Allocation by Purpose</CardTitle>
                    <Target className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <CardDescription>How equipment is being utilized</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-64">
                    {/* Donut Chart Visualization */}
                    <div className="relative w-48 h-48">
                      <svg viewBox="0 0 100 100" className="w-full h-full">
                        {/* Calculate the donut segments */}
                        {(() => {
                          let startAngle = 0;
                          const total = assetAllocationByPurpose.reduce((sum, item) => sum + item.percentage, 0);

                          return assetAllocationByPurpose.map((item, index) => {
                            const angle = (item.percentage / total) * 360;
                            const endAngle = startAngle + angle;

                            // Calculate the SVG arc path
                            const x1 = 50 + 35 * Math.cos((startAngle * Math.PI) / 180);
                            const y1 = 50 + 35 * Math.sin((startAngle * Math.PI) / 180);
                            const x2 = 50 + 35 * Math.cos((endAngle * Math.PI) / 180);
                            const y2 = 50 + 35 * Math.sin((endAngle * Math.PI) / 180);

                            const largeArcFlag = angle > 180 ? 1 : 0;

                            const pathData = `M 50 50 L ${x1} ${y1} A 35 35 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;

                            const path = (
                              <path
                                key={index}
                                d={pathData}
                                fill={item.color}
                                stroke="#fff"
                                strokeWidth="1"
                              />
                            );

                            startAngle = endAngle;
                            return path;
                          });
                        })()}

                        {/* Add a white circle in the center for the donut hole */}
                        <circle cx="50" cy="50" r="20" fill="white" />
                      </svg>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 mt-4">
                    {assetAllocationByPurpose.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
                        <div className="text-sm">{item.purpose}</div>
                        <div className="ml-auto text-sm font-medium">{item.percentage}%</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Quick Links */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Link to="/masters/vendors" className="no-underline">
                <Card className="hover:bg-gray-50 transition-colors cursor-pointer">
                  <CardContent className="p-4 flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Truck className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Vendors</h3>
                      <p className="text-xs text-muted-foreground">Manage suppliers</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link to="/transaction/consumables/orders" className="no-underline">
                <Card className="hover:bg-gray-50 transition-colors cursor-pointer">
                  <CardContent className="p-4 flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <ShoppingCart className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Orders</h3>
                      <p className="text-xs text-muted-foreground">Track purchases</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link to="/masters/rolling-cage" className="no-underline">
                <Card className="hover:bg-gray-50 transition-colors cursor-pointer">
                  <CardContent className="p-4 flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Package className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Rolling Cage</h3>
                      <p className="text-xs text-muted-foreground">Manage inventory</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link to="/user-management/users" className="no-underline">
                <Card className="hover:bg-gray-50 transition-colors cursor-pointer">
                  <CardContent className="p-4 flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Users className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium">Users</h3>
                      <p className="text-xs text-muted-foreground">Manage accounts</p>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        ) : (          <EnhancedStatewideOverview 
            data={transformedStatewideData}
            selectedCounty={selectedCounty}
            selectedAssetType={selectedEquipmentType}
            selectedStatus="all"
            onFilterChange={(type, value) => {
              if (type === 'county') setSelectedCounty(value);
              if (type === 'assetType') setSelectedEquipmentType(value);
            }}
          />
        )}

        {/* Close dashboard content wrapper */}
        </>
        )}
      </div>
    </AppLayout>
  );
};

export default Dashboard;
