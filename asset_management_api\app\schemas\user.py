# app/schemas/user.py - FIXED VERSION TO MATCH FRONTEND
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Any, Dict, Literal
from datetime import datetime

class UserBase(BaseModel):
    first_name: str = Field(..., min_length=1, max_length=255)
    last_name: str = Field(..., min_length=1, max_length=255)
    email: EmailStr
    mobile: str = Field(..., min_length=1, max_length=20)
    user_group: str = Field(..., min_length=1, max_length=100)
    login_enabled: bool = True
    login_id: str = Field(..., min_length=1, max_length=100)
    access_level: Literal["county", "state", "precinct"]  # Using Literal instead of Enum
    state: Optional[str] = Field(None, max_length=100)
    county: Optional[str] = Field(None, max_length=100)
    precinct: Optional[str] = Field(None, max_length=100)
    status: bool = True
    image: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=255)
    employee_no: Optional[str] = Field(None, max_length=50)
    manager: Optional[str] = Field(None, max_length=255)
    department: Optional[str] = Field(None, max_length=255)
    location: Optional[str] = Field(None, max_length=255)
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    pincode: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)
    role: Literal["admin", "manager", "user", "Portal Admin"] = "user"  # Using Literal instead of Enum
    username: Optional[str] = Field(None, max_length=100)

class UserCreate(UserBase):
    password: str = Field(..., min_length=6, max_length=255)

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Password must be at least 6 characters long')
        return v

    @validator('county')
    def validate_geographical_scope(cls, v, values):
        """Validate geographical scope based on access level."""
        access_level = values.get('access_level')
        state = values.get('state')
        precinct = values.get('precinct')

        if access_level == 'state':
            if not state:
                raise ValueError('State is required for state-level access')
        elif access_level == 'county':
            if not state or not v:
                raise ValueError('State and county are required for county-level access')
        elif access_level == 'precinct':
            if not state or not v or not precinct:
                raise ValueError('State, county, and precinct are required for precinct-level access')

        return v

# Flexible login schema that matches frontend
class UserLogin(BaseModel):
    email: str = Field(..., min_length=1)  # Can be email, username, or loginId
    password: str = Field(..., min_length=1)

    class Config:
        # Allow extra fields to be passed through
        extra = "ignore"

# Alternative login schema for dict input (what frontend actually sends)
class FlexibleUserLogin(BaseModel):
    email: Optional[str] = None
    username: Optional[str] = None
    loginId: Optional[str] = None
    password: str = Field(..., min_length=1)

    @validator('password')
    def validate_login_fields(cls, v, values):
        email = values.get('email')
        username = values.get('username') 
        login_id = values.get('loginId')
        
        if not any([email, username, login_id]):
            raise ValueError('Email, username, or loginId is required')
        return v

class UserUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=1, max_length=255)
    last_name: Optional[str] = Field(None, min_length=1, max_length=255)
    email: Optional[EmailStr] = None
    mobile: Optional[str] = Field(None, min_length=1, max_length=20)
    user_group: Optional[str] = Field(None, min_length=1, max_length=100)
    login_enabled: Optional[bool] = None
    login_id: Optional[str] = Field(None, min_length=1, max_length=100)
    access_level: Optional[Literal["county", "state", "precinct"]] = None
    county: Optional[str] = Field(None, max_length=100)
    precinct: Optional[str] = Field(None, max_length=100)
    status: Optional[bool] = None
    image: Optional[str] = Field(None, max_length=500)
    company: Optional[str] = Field(None, max_length=255)
    employee_no: Optional[str] = Field(None, max_length=50)
    manager: Optional[str] = Field(None, max_length=255)
    department: Optional[str] = Field(None, max_length=255)
    location: Optional[str] = Field(None, max_length=255)
    address_line1: Optional[str] = Field(None, max_length=255)
    address_line2: Optional[str] = Field(None, max_length=255)
    city: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    pincode: Optional[str] = Field(None, max_length=20)
    country: Optional[str] = Field(None, max_length=100)
    role: Optional[Literal["admin", "manager", "user", "Portal Admin"]] = None
    username: Optional[str] = Field(None, max_length=100)

class UserResponse(UserBase):
    id: str
    created_at: datetime
    updated_at: datetime
    full_name: str

    class Config:
        from_attributes = True

# Simplified user profile that matches frontend expectations
class UserProfile(BaseModel):
    id: str
    first_name: str
    last_name: str
    full_name: str
    email: str
    mobile: str
    user_group: str
    login_enabled: bool
    login_id: str
    access_level: Literal["county", "state", "precinct"]
    county: Optional[str]
    precinct: Optional[str]
    status: bool
    image: Optional[str]
    company: Optional[str]
    employee_no: Optional[str]
    manager: Optional[str]
    department: Optional[str]
    location: Optional[str]
    address_line1: Optional[str]
    address_line2: Optional[str]
    city: Optional[str]
    state: Optional[str]
    pincode: Optional[str]
    country: Optional[str]
    role: Literal["admin", "manager", "user", "Portal Admin"]
    username: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Additional fields for permissions
    permissions: Optional[Dict[str, Any]] = None
    default_route: Optional[str] = None

    class Config:
        from_attributes = True

# Token response that matches frontend expectations exactly
class TokenResponse(BaseModel):
    success: bool = True
    message: str = "Login successful"
    data: Dict[str, Any]  # Flexible data structure

class PasswordChange(BaseModel):
    current_password: str = Field(..., min_length=1, alias="currentPassword")
    new_password: str = Field(..., min_length=6, max_length=255, alias="newPassword")

    class Config:
        populate_by_name = True

    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 6:
            raise ValueError('Password must be at least 6 characters long')
        return v

# Error response format
class ErrorResponse(BaseModel):
    success: bool = False
    message: str
    status_code: Optional[int] = None

# Success response format  
class SuccessResponse(BaseModel):
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None

# Additional response models for user management
class UserListResponse(BaseModel):
    success: bool = True
    data: Dict[str, Any]
    message: Optional[str] = None

class UserGroupResponse(BaseModel):
    id: str
    name: str
    members: int
    status: bool
    created_at: str

class UserStatsResponse(BaseModel):
    success: bool
    data: Dict[str, Any]