# app/routes/asset_transfers.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.config.database import get_db
from app.models.asset_transfers import AssetTransfer, TransferStatus, TransferType
from app.models.assets import Asset
from app.models.user import User
from app.middleware.auth import get_current_user
from app.middleware.rbac import get_rbac_service, RBACService, require_admin
from app.schemas.asset_transfers import (
    AssetTransferCreate, AssetTransferUpdate, AssetTransferResponse
)
from app.services.asset_workflow_service import AssetWorkflowService, WorkflowModules
from typing import Optional, List
import logging
from datetime import datetime
import uuid

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/", response_model=AssetTransferResponse, status_code=status.HTTP_201_CREATED)
async def create_asset_transfer(
    transfer_data: AssetTransferCreate,
    rbac: RBACService = Depends(get_rbac_service),
    db: Session = Depends(get_db)
):
    """
    Create a new asset transfer with RBAC validation.
    Initiates the transfer process and updates asset status to IN_TRANSFER.
    """
    try:
        # Validate asset exists and is in correct status
        asset = db.query(Asset).filter(Asset.id == transfer_data.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )

        # Check RBAC permissions for the asset
        if not rbac.can_access_asset(asset):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to access this asset"
            )

        # Check RBAC permissions for initiating transfer between locations
        from_location = {
            "state": asset.state,
            "county": transfer_data.from_county or asset.county,
            "precinct": transfer_data.from_precinct or asset.precinct
        }
        to_location = {
            "state": asset.state,  # Assuming transfers within same state for now
            "county": transfer_data.to_county,
            "precinct": transfer_data.to_precinct
        }

        if not rbac.can_initiate_transfer(from_location, to_location):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to initiate transfer between these locations"
            )
        
        # Check if asset can be transferred (should be CHECKED_OUT)
        workflow_service = AssetWorkflowService(db)
        can_transition, message = workflow_service.can_transition(
            asset_id=transfer_data.asset_id,
            to_status="In-transfer",
            workflow_module=WorkflowModules.TRANSFER,
            user_id=rbac.current_user.id,
            context={"transfer_type": transfer_data.transfer_type.value}
        )
        
        if not can_transition:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot initiate transfer: {message}"
            )
        
        # Generate unique transfer ID
        transfer_id = f"TR-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Create transfer record
        transfer = AssetTransfer(
            transfer_id=transfer_id,
            asset_id=transfer_data.asset_id,
            transfer_type=transfer_data.transfer_type,
            status=TransferStatus.INITIATED,
            from_location=transfer_data.from_location,
            to_location=transfer_data.to_location,
            from_county=transfer_data.from_county,
            to_county=transfer_data.to_county,
            from_precinct=transfer_data.from_precinct,
            to_precinct=transfer_data.to_precinct,
            initiated_by=rbac.current_user.id,
            assigned_to=transfer_data.assigned_to,
            assigned_email=transfer_data.assigned_email,
            assigned_phone=transfer_data.assigned_phone,
            expected_delivery_date=transfer_data.expected_delivery_date,
            condition_before_transfer=asset.condition.value,
            notes=transfer_data.notes,
            special_instructions=transfer_data.special_instructions
        )
        
        db.add(transfer)
        db.flush()  # Get the ID
        
        # Update asset status to IN_TRANSFER
        success, transition_message = workflow_service.transition_asset_status(
            asset_id=transfer_data.asset_id,
            to_status="In-transfer",
            workflow_module=WorkflowModules.TRANSFER,
            user_id=rbac.current_user.id,
            change_reason=f"Transfer initiated: {transfer_id}",
            session_id=transfer_id,
            notes=f"Transfer from {transfer_data.from_location} to {transfer_data.to_location}",
            context={
                "transfer_id": transfer_id,
                "transfer_type": transfer_data.transfer_type.value,
                "to_location": transfer_data.to_location
            }
        )
        
        if not success:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update asset status: {transition_message}"
            )
        
        db.commit()
        db.refresh(transfer)
        
        logger.info(f"Transfer {transfer_id} created for asset {asset.asset_id} by user {rbac.current_user.id}")
        return transfer
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating asset transfer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error creating transfer"
        )

@router.get("/", response_model=dict)
async def get_asset_transfers(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    asset_id: Optional[int] = Query(None),
    status: Optional[TransferStatus] = Query(None),
    transfer_type: Optional[TransferType] = Query(None),
    from_location: Optional[str] = Query(None),
    to_location: Optional[str] = Query(None),
    rbac: RBACService = Depends(get_rbac_service),
    db: Session = Depends(get_db)
):
    """Get asset transfers with RBAC filtering and pagination."""
    try:
        # Calculate offset
        offset = (page - 1) * limit

        # Build query with RBAC filtering
        query = db.query(AssetTransfer)

        # Apply RBAC filters first (most important for security)
        query = rbac.apply_transfer_filters(query)
        
        # Apply filters
        if asset_id:
            query = query.filter(AssetTransfer.asset_id == asset_id)
        if status:
            query = query.filter(AssetTransfer.status == status)
        if transfer_type:
            query = query.filter(AssetTransfer.transfer_type == transfer_type)
        if from_location:
            query = query.filter(AssetTransfer.from_location.ilike(f"%{from_location}%"))
        if to_location:
            query = query.filter(AssetTransfer.to_location.ilike(f"%{to_location}%"))
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        transfers = query.order_by(AssetTransfer.created_at.desc()).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        return {
            "transfers": transfers,
            "pagination": {
                "page": page,
                "limit": limit,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching asset transfers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching transfers"
        )

@router.get("/{transfer_id}", response_model=AssetTransferResponse)
async def get_asset_transfer(
    transfer_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific asset transfer by ID."""
    transfer = db.query(AssetTransfer).filter(AssetTransfer.transfer_id == transfer_id).first()
    if not transfer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Asset transfer not found"
        )
    return transfer

@router.put("/{transfer_id}", response_model=AssetTransferResponse)
async def update_asset_transfer(
    transfer_id: str,
    update_data: AssetTransferUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update asset transfer details and status.
    Handles status transitions with proper workflow validation.
    """
    try:
        # Get transfer
        transfer = db.query(AssetTransfer).filter(AssetTransfer.transfer_id == transfer_id).first()
        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset transfer not found"
            )
        
        # Update basic fields
        for field, value in update_data.dict(exclude_unset=True).items():
            if field != "status" and hasattr(transfer, field):
                setattr(transfer, field, value)
        
        # Handle status change with workflow validation
        if update_data.status and update_data.status != transfer.status:
            workflow_service = AssetWorkflowService(db)
            
            # Map transfer status to asset status
            asset_status_mapping = {
                TransferStatus.IN_TRANSIT: "In-transfer",
                TransferStatus.DELIVERED: "Delivered",
                TransferStatus.FAILED: "Damaged",
                TransferStatus.CANCELLED: "Checked-out"  # Return to previous status
            }
            
            new_asset_status = asset_status_mapping.get(update_data.status)
            if new_asset_status:
                # Update asset status
                success, message = workflow_service.transition_asset_status(
                    asset_id=transfer.asset_id,
                    to_status=new_asset_status,
                    workflow_module=WorkflowModules.TRANSFER,
                    user_id=current_user.id,
                    change_reason=f"Transfer status changed to {update_data.status.value}",
                    session_id=transfer_id,
                    context={
                        "transfer_status": update_data.status.value,
                        "actual_delivery_date": update_data.actual_delivery_date
                    }
                )
                
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Cannot update transfer status: {message}"
                    )
            
            # Update transfer status
            transfer.status = update_data.status
            
            # Set completion timestamp for final statuses
            if update_data.status in [TransferStatus.DELIVERED, TransferStatus.FAILED, TransferStatus.CANCELLED]:
                transfer.completed_at = datetime.utcnow()
        
        db.commit()
        db.refresh(transfer)
        
        logger.info(f"Transfer {transfer_id} updated by user {current_user.id}")
        return transfer
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating asset transfer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error updating transfer"
        )

@router.post("/{transfer_id}/verify", response_model=AssetTransferResponse)
async def verify_asset_transfer(
    transfer_id: str,
    verification_code: str = Query(..., description="Verification code"),
    condition_after_transfer: Optional[str] = Query(None, description="Asset condition after transfer"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Verify asset transfer delivery.
    Updates transfer status to DELIVERED and asset status accordingly.
    """
    try:
        # Get transfer
        transfer = db.query(AssetTransfer).filter(AssetTransfer.transfer_id == transfer_id).first()
        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset transfer not found"
            )
        
        # Validate verification code
        if transfer.verification_code != verification_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        # Update transfer
        transfer.status = TransferStatus.DELIVERED
        transfer.verified_by = current_user.id
        transfer.verified_at = datetime.utcnow()
        transfer.actual_delivery_date = datetime.utcnow()
        transfer.completed_at = datetime.utcnow()
        
        if condition_after_transfer:
            transfer.condition_after_transfer = condition_after_transfer
        
        # Update asset status to DELIVERED
        workflow_service = AssetWorkflowService(db)
        success, message = workflow_service.transition_asset_status(
            asset_id=transfer.asset_id,
            to_status="Delivered",
            workflow_module=WorkflowModules.TRANSFER,
            user_id=current_user.id,
            change_reason=f"Transfer delivery verified: {transfer_id}",
            session_id=transfer_id,
            notes=f"Verified by {current_user.username if hasattr(current_user, 'username') else current_user.id}",
            context={
                "verification_code": verification_code,
                "condition_after_transfer": condition_after_transfer
            }
        )
        
        if not success:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update asset status: {message}"
            )
        
        db.commit()
        db.refresh(transfer)
        
        logger.info(f"Transfer {transfer_id} verified by user {current_user.id}")
        return transfer
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error verifying asset transfer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error verifying transfer"
        )

@router.get("/asset/{asset_id}/history", response_model=List[AssetTransferResponse])
async def get_asset_transfer_history(
    asset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get transfer history for a specific asset."""
    transfers = db.query(AssetTransfer).filter(
        AssetTransfer.asset_id == asset_id
    ).order_by(AssetTransfer.created_at.desc()).all()
    
    return transfers
