import { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Plus, FileDown, Printer, Edit, Trash, CheckCircle, XCircle, Copy, Filter, PlayCircle, Package, PackageOpen, AlertTriangle } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AssetViewDialog } from "@/components/assets/AssetViewDialog";
import { AssetEditDialog } from "@/components/assets/AssetEditDialog";
import { AssetAddDialog } from "@/components/assets/AssetAddDialog";
import { LAChecklistDialog } from "@/components/la-checklist/LAChecklistDialog";
import { PackAssetsDialog } from "@/components/supply-checklist/PackAssetsDialog";
import { UnpackAssetsDialog } from "@/components/supply-checklist/UnpackAssetsDialog";
import { EnhancedAssetFilters } from "@/components/assets/EnhancedAssetFilters";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";
import { assetService } from "@/services/assetService";
import { workflowService } from "@/services/workflowService";

// Asset interface
interface Asset {
  id: string;
  asset_id: string;
  type: string;
  model?: string;
  serial_number?: string;
  status: string;
  condition: string;
  location: string;
  assigned_to?: string;
  state: string;
  county: string;
  precinct?: string;
  purchase_date?: string;
  warranty_expiry?: string;
  last_maintenance?: string;
  next_maintenance?: string;
  last_checked?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export default function AssetsPage() {
  // Backend data states
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for search and filters
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedLocation, setSelectedLocation] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedOwnership, setSelectedOwnership] = useState("all");
  const [groupBy, setGroupBy] = useState<"none" | "category" | "location">("none");

  // State for assets and dialogs
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);
  const [groupedAssets, setGroupedAssets] = useState<Record<string, Asset[]>>({});
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [showViewDialog, setShowViewDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showAddDialog, setShowAddDialog] = useState(false);

  // Workflow dialog states
  const [showLAChecklistDialog, setShowLAChecklistDialog] = useState(false);
  const [showPackDialog, setShowPackDialog] = useState(false);
  const [showUnpackDialog, setShowUnpackDialog] = useState(false);
  const [packType, setPackType] = useState<'PACKING_LIST' | 'ROLLING_CAGE'>('PACKING_LIST');

  // Load assets from backend
  useEffect(() => {
    const loadAssets = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await assetService.getAssets({});

        // Handle both paginated and direct response formats
        if (response.success && response.data) {
          setAssets(response.data);
        } else if (response.data) {
          // Direct array response
          setAssets(response.data);
        } else if (Array.isArray(response)) {
          // Direct array response
          setAssets(response);
        } else {
          setError('Failed to load assets');
          setAssets([]);
        }
      } catch (err) {
        console.error('Error loading assets:', err);
        setError('Failed to load assets');
        setAssets([]);
      } finally {
        setLoading(false);
      }
    };

    loadAssets();
  }, []);

  // Apply all filters
  const applyFilters = () => {
    let filtered = assets;

    // Apply search filter
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        asset =>
          asset.asset_id.toLowerCase().includes(query) ||
          asset.type.toLowerCase().includes(query) ||
          (asset.model && asset.model.toLowerCase().includes(query)) ||
          asset.status.toLowerCase().includes(query) ||
          asset.location.toLowerCase().includes(query) ||
          (asset.serial_number && asset.serial_number.toLowerCase().includes(query))
      );
    }

    // Apply category filter (using type field)
    if (selectedCategory !== "all") {
      filtered = filtered.filter(asset => asset.type === selectedCategory);
    }

    // Apply location filter
    if (selectedLocation !== "all") {
      filtered = filtered.filter(asset =>
        asset.location && asset.location.includes(selectedLocation)
      );
    }

    // Apply status filter
    if (selectedStatus !== "all") {
      filtered = filtered.filter(asset => asset.status === selectedStatus);
    }

    // Apply ownership filter (using assigned_to field)
    if (selectedOwnership !== "all") {
      filtered = filtered.filter(asset =>
        selectedOwnership === "County" ? !asset.assigned_to : !!asset.assigned_to
      );
    }

    setFilteredAssets(filtered);
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchQuery("");
    setSelectedCategory("all");
    setSelectedLocation("all");
    setSelectedStatus("all");
    setSelectedOwnership("all");
    setGroupBy("none");
  };

  // Apply filters when any filter value changes or assets data changes
  useEffect(() => {
    applyFilters();
  }, [assets, searchQuery, selectedCategory, selectedLocation, selectedStatus, selectedOwnership]);

  // Group assets when filtered assets or groupBy changes
  useEffect(() => {
    if (groupBy === "none") {
      setGroupedAssets({});
      return;
    }

    const grouped: Record<string, any[]> = {};

    filteredAssets.forEach(asset => {
      let groupKey = "";

      if (groupBy === "category") {
        groupKey = asset.type || "Uncategorized";
      } else if (groupBy === "location") {
        groupKey = asset.location || "Unassigned";
      } else if (groupBy === "status") {
        groupKey = asset.status || "Unknown";
      }

      if (!grouped[groupKey]) {
        grouped[groupKey] = [];
      }

      grouped[groupKey].push(asset);
    });

    setGroupedAssets(grouped);
  }, [filteredAssets, groupBy]);

  // Handle viewing an asset
  const handleViewAsset = (asset: any) => {
    setSelectedAsset(asset);
    setShowViewDialog(true);
  };

  // Handle editing an asset
  const handleEditAsset = (asset: any) => {
    setSelectedAsset(asset);
    setShowEditDialog(true);
  };

  // Handle adding a new asset
  const handleAddAsset = () => {
    setShowAddDialog(true);
  };

  // Handle workflow actions
  const handleLAChecklist = () => {
    setShowLAChecklistDialog(true);
  };

  const handlePackAssets = (type: 'PACKING_LIST' | 'ROLLING_CAGE') => {
    setPackType(type);
    setShowPackDialog(true);
  };

  const handleUnpackAssets = (type: 'PACKING_LIST' | 'ROLLING_CAGE') => {
    setPackType(type);
    setShowUnpackDialog(true);
  };

  const handleWorkflowComplete = () => {
    // Refresh the assets list when workflow operations complete
    // In a real app, you would fetch fresh data from the API
    console.log('Workflow completed - refreshing data');
  };

  // Handle exporting data in various formats
  const handleExport = (format: string) => {
    // Prepare data for export with proper column names
    const exportableData = filteredAssets.map(asset => ({
      ID: asset.id,
      Tag: asset.asset_id,
      "Owned By": asset.assigned_to || 'County',
      Category: asset.type,
      Model: asset.model || 'N/A',
      "Serial Number": asset.serial_number || 'N/A',
      Location: asset.location,
      Status: asset.status,
      Condition: asset.condition,
      "Last Updated": new Date(asset.updated_at).toLocaleDateString()
    }));

    // Define column headers in the desired order
    const headers = ['ID', 'Tag', 'Owned By', 'Category', 'Model', 'Serial Number', 'Location', 'Status', 'Condition', 'Last Updated'];

    // Call the export function with the prepared data
    exportData(exportableData, format, 'assets', 'Assets', headers);
  };

  // Handle printing assets with custom formatting and timestamp
  const handlePrint = () => {
    // Prepare data for print with proper column names
    const printableData = filteredAssets.map(asset => ({
      Tag: asset.asset_id,
      "Owned By": asset.assigned_to || 'County',
      Category: asset.type,
      Model: asset.model || 'N/A',
      Location: asset.location,
      Status: asset.status,
      "Last Updated": new Date(asset.updated_at).toLocaleDateString()
    }));

    // Define column headers in the desired order
    const headers = ['Tag', 'Owned By', 'Category', 'Model', 'Location', 'Status', 'Last Updated'];

    // Use the existing print function from exportUtils
    exportData(printableData, 'print', 'assets', 'Assets', headers);
  };



  return (
    <AppLayout>
      <div className="p-6 space-y-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 pb-4 border-b">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Assets</h1>
            <p className="mt-1 text-gray-500">Manage all election equipment and assets</p>
          </div>
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            {/* Workflow Buttons */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="mr-2">
                  <PlayCircle className="h-4 w-4 mr-2" />
                  L&A Checklist
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={handleLAChecklist}
                >
                  <PlayCircle className="h-4 w-4 mr-2" />
                  <span>Start New Session</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="mr-2">
                  <Package className="h-4 w-4 mr-2" />
                  Pack Assets
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handlePackAssets('PACKING_LIST')}
                >
                  <Package className="h-4 w-4 mr-2" />
                  <span>Pack to Packing List</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handlePackAssets('ROLLING_CAGE')}
                >
                  <Package className="h-4 w-4 mr-2" />
                  <span>Pack to Rolling Cage</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="mr-2">
                  <PackageOpen className="h-4 w-4 mr-2" />
                  Unpack Assets
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleUnpackAssets('PACKING_LIST')}
                >
                  <PackageOpen className="h-4 w-4 mr-2" />
                  <span>Unpack from Packing List</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleUnpackAssets('ROLLING_CAGE')}
                >
                  <PackageOpen className="h-4 w-4 mr-2" />
                  <span>Unpack from Rolling Cage</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Export/Print Buttons */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="mr-2">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleExport('pdf')}
                >
                  <div className="h-5 w-5 mr-2 text-red-500 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                      <path d="M7 3C5.9 3 5 3.9 5 5v14c0 1.1 0.9 2 2 2h10c1.1 0 2-0.9 2-2V5c0-1.1-0.9-2-2-2H7zm0 2h10v14H7V5zm2 2v2h6V7H9zm0 4v2h6v-2H9zm0 4v2h4v-2H9z"/>
                    </svg>
                  </div>
                  <span>PDF Document</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleExport('excel')}
                >
                  <div className="h-5 w-5 mr-2 text-green-600 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                      <path d="M3 5v14c0 1.1 0.9 2 2 2h14c1.1 0 2-0.9 2-2V5c0-1.1-0.9-2-2-2H5c-1.1 0-2 0.9-2 2zm16 14H5V5h14v14zM7 11h2v2H7v-2zm0-4h2v2H7V7zm4 4h2v2h-2v-2zm0-4h2v2h-2V7zm4 4h2v2h-2v-2zm0-4h2v2h-2V7z"/>
                    </svg>
                  </div>
                  <span>Excel Spreadsheet</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleExport('csv')}
                >
                  <div className="h-5 w-5 mr-2 text-blue-600 flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                      <path d="M14 2H6C4.9 2 4 2.9 4 4v16c0 1.1 0.9 2 2 2h12c1.1 0 2-0.9 2-2V8l-6-6zM6 4h7l5 5v11H6V4zm2 8v2h8v-2H8zm0 4v2h5v-2H8z"/>
                    </svg>
                  </div>
                  <span>CSV File</span>
                </DropdownMenuItem>

                <DropdownMenuItem
                  className="flex items-center cursor-pointer"
                  onClick={() => handleExport('copy')}
                >
                  <div className="h-5 w-5 mr-2 text-gray-600 flex items-center justify-center">
                    <Copy className="w-4 h-4" />
                  </div>
                  <span>Copy to Clipboard</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" className="mr-2" onClick={handlePrint}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button size="sm" onClick={handleAddAsset}>
              <Plus className="h-4 w-4 mr-2" />
              Add Asset
            </Button>
          </div>
        </div>

        <div className="mb-6">
          <EnhancedAssetFilters
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            selectedCategory={selectedCategory}
            setSelectedCategory={setSelectedCategory}
            selectedLocation={selectedLocation}
            setSelectedLocation={setSelectedLocation}
            selectedStatus={selectedStatus}
            setSelectedStatus={setSelectedStatus}
            selectedOwnership={selectedOwnership}
            setSelectedOwnership={setSelectedOwnership}
            groupBy={groupBy}
            setGroupBy={setGroupBy}
            applyFilters={applyFilters}
            resetFilters={resetFilters}
          />
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading assets...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}

        {/* Assets Content - Only show when not loading */}
        {!loading && (
          <>
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm text-muted-foreground">
                Showing <span className="font-medium">{filteredAssets.length}</span> of <span className="font-medium">{assets.length}</span> assets
                {(selectedCategory !== "all" || selectedLocation !== "all" || selectedStatus !== "all" || selectedOwnership !== "all") && (
                  <span> (filtered)</span>
                )}
              </div>

              {filteredAssets.length === 0 && assets.length > 0 && (
                <div className="text-sm text-amber-600 flex items-center">
                  <Filter className="h-3 w-3 mr-1" />
                  No assets match the current filters. Try adjusting your filters.
                </div>
              )}
            </div>

        {groupBy === "none" ? (
          // Regular table view (no grouping)
          <div className="border rounded-lg shadow-sm overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                                          <TableHead className="py-3 text-gray-700">Tag</TableHead>
                        <TableHead className="py-3 text-gray-700">Owned By</TableHead>
                        <TableHead className="py-3 text-gray-700">Category</TableHead>
                        <TableHead className="py-3 text-gray-700">Model</TableHead>
                        <TableHead className="py-3 text-gray-700">Location</TableHead>
                        <TableHead className="py-3 text-gray-700">Status</TableHead>
                        <TableHead className="py-3 text-gray-700">Last Updated</TableHead>
                  <TableHead className="py-3 text-gray-700 text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssets.map((asset) => (
                  <TableRow key={asset.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <TableCell className="py-3">{asset.asset_id}</TableCell>
                    <TableCell className="py-3">{asset.assigned_to || 'County'}</TableCell>
                    <TableCell className="py-3">{asset.type}</TableCell>
                    <TableCell className="py-3">{asset.model || 'N/A'}</TableCell>
                    <TableCell className="py-3">{asset.location}</TableCell>
                    <TableCell className="py-3">
                      <div className="flex items-center gap-2">
                        <div className={`h-3 w-3 rounded-full ${
                          asset.status === "Ready" ? "bg-green-500" :
                          asset.status === "New" ? "bg-blue-500" :
                          asset.status === "Failed" ? "bg-red-500" :
                          asset.status === "Packed" ? "bg-purple-500" :
                          asset.status === "Checked-out" ? "bg-orange-500" :
                          asset.status === "In-transfer" ? "bg-yellow-500" :
                          asset.status === "Delivered" ? "bg-teal-500" :
                          asset.status === "Using" ? "bg-indigo-500" :
                          asset.status === "Damaged" ? "bg-red-600" :
                          asset.status === "Under Maintenance" ? "bg-amber-500" :
                          asset.status === "Completed" ? "bg-green-600" :
                          asset.status === "Retired" ? "bg-gray-500" : "bg-gray-400"
                        }`}></div>
                        <span>{asset.status}</span>
                      </div>
                    </TableCell>
                    <TableCell className="py-3">{new Date(asset.updated_at).toLocaleDateString()}</TableCell>
                    <TableCell className="py-3 text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 mr-1"
                        onClick={() => handleViewAsset(asset)}
                      >
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                        onClick={() => handleEditAsset(asset)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          // Grouped view
          <div className="space-y-6">
            {Object.keys(groupedAssets).length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No assets match the current filters
              </div>
            ) : (
              Object.entries(groupedAssets).map(([groupName, assets]) => (
                <div key={groupName} className="border rounded-lg shadow-sm overflow-hidden">
                  <div className="bg-gray-100 px-4 py-3 font-medium flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="text-gray-800">{groupName}</span>
                      <span className="ml-2 text-sm text-gray-500">({assets.length} items)</span>
                    </div>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="py-3 text-gray-700">Tag</TableHead>
                        <TableHead className="py-3 text-gray-700">Owned By</TableHead>
                        <TableHead className="py-3 text-gray-700">
                          {groupBy === "category" ? "Model" : "Category"}
                        </TableHead>
                        <TableHead className="py-3 text-gray-700">Name</TableHead>
                        <TableHead className="py-3 text-gray-700">Status</TableHead>
                        <TableHead className="py-3 text-gray-700">Last Updated</TableHead>
                        <TableHead className="py-3 text-gray-700 text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {assets.map((asset) => (
                        <TableRow key={asset.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <TableCell className="py-3">{asset.asset_id}</TableCell>
                          <TableCell className="py-3">{asset.assigned_to || 'County'}</TableCell>
                          <TableCell className="py-3">
                            {groupBy === "category" ? (asset.model || 'N/A') : asset.type}
                          </TableCell>
                          <TableCell className="py-3">{asset.location}</TableCell>
                          <TableCell className="py-3">
                            <div className="flex items-center gap-2">
                              <div className={`h-3 w-3 rounded-full ${
                                asset.status === "Ready" ? "bg-green-500" :
                                asset.status === "New" ? "bg-blue-500" :
                                asset.status === "Failed" ? "bg-red-500" :
                                asset.status === "Packed" ? "bg-purple-500" :
                                asset.status === "Checked-out" ? "bg-orange-500" :
                                asset.status === "In-transfer" ? "bg-yellow-500" :
                                asset.status === "Delivered" ? "bg-teal-500" :
                                asset.status === "Using" ? "bg-indigo-500" :
                                asset.status === "Damaged" ? "bg-red-600" :
                                asset.status === "Under Maintenance" ? "bg-amber-500" :
                                asset.status === "Completed" ? "bg-green-600" :
                                asset.status === "Retired" ? "bg-gray-500" : "bg-gray-400"
                              }`}></div>
                              <span>{asset.status}</span>
                            </div>
                          </TableCell>
                          <TableCell className="py-3">{new Date(asset.updated_at).toLocaleDateString()}</TableCell>
                          <TableCell className="py-3 text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 mr-1"
                              onClick={() => handleViewAsset(asset)}
                            >
                              View
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                              onClick={() => handleEditAsset(asset)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ))
            )}
          </div>
        )}
          </>
        )}

      {/* View Asset Dialog */}
      {showViewDialog && selectedAsset && (
        <AssetViewDialog
          open={showViewDialog}
          onOpenChange={setShowViewDialog}
          asset={selectedAsset}
        />
      )}

      {/* Edit Asset Dialog */}
      {showEditDialog && selectedAsset && (
        <AssetEditDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          asset={selectedAsset}
        />
      )}

      {/* Add Asset Dialog */}
      {showAddDialog && (
        <AssetAddDialog
          open={showAddDialog}
          onOpenChange={setShowAddDialog}
        />
      )}

      {/* Workflow Dialogs */}
      {showLAChecklistDialog && (
        <LAChecklistDialog
          open={showLAChecklistDialog}
          onOpenChange={setShowLAChecklistDialog}
          onSessionComplete={handleWorkflowComplete}
        />
      )}

      {showPackDialog && (
        <PackAssetsDialog
          open={showPackDialog}
          onOpenChange={setShowPackDialog}
          packType={packType}
          onPackComplete={handleWorkflowComplete}
        />
      )}

      {showUnpackDialog && (
        <UnpackAssetsDialog
          open={showUnpackDialog}
          onOpenChange={setShowUnpackDialog}
          containerType={packType}
          onUnpackComplete={handleWorkflowComplete}
        />
      )}

      </div>
    </AppLayout>
  );
}
