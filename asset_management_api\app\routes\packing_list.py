# app/routes/packing_list.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.models.packing_list import PackingList
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from uuid import uuid4
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/")
async def get_packing_lists(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all packing lists.
    Equivalent to GET / in Node.js packingList.ts
    """
    try:
        packing_lists = db.query(PackingList).order_by(PackingList.created_at.desc()).all()
        
        return {
            "success": True,
            "data": {
                "packingLists": [packing_list.to_dict() for packing_list in packing_lists],
                "count": len(packing_lists)
            }
        }
    except Exception as e:
        logger.error(f"Get packing lists error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{list_id}")
async def get_packing_list(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get packing list by ID with all related data.
    Equivalent to GET /:id in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        return {
            "success": True,
            "data": {"packingList": packing_list.to_dict()}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get packing list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/")
async def create_packing_list(
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create new packing list.
    Equivalent to POST / in Node.js packingList.ts
    """
    try:
        election = packing_list_data.get("election")
        to = packing_list_data.get("to")
        from_location = packing_list_data.get("from")
        supply_package = packing_list_data.get("supplyPackage")
        item = packing_list_data.get("item")
        qty = packing_list_data.get("qty")
        
        if not all([election, to, from_location, supply_package, item]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing required fields"
            )
        
        packing_list = PackingList(
            id=str(uuid4()),
            election=election,
            to=to,
            from_location=from_location,
            supply_package=supply_package,
            item=item,
            qty=qty or 1,
            verification_items=[],
            completion_status={
                "allItemsPacked": False,
                "allItemsVerified": False,
                "deliveryInformationAdded": False,
                "finalApproval": False,
                "itemsPackedCount": 0,
                "totalItemsCount": 0,
                "itemsVerifiedCount": 0
            },
            unpack_items=[]
        )
        
        db.add(packing_list)
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Packing list created successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}")
async def update_packing_list(
    list_id: str,
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update packing list.
    Equivalent to PUT /:id in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        # Update fields
        for field, value in packing_list_data.items():
            if field == "from":
                packing_list.from_location = value
            elif field == "supplyPackage":
                packing_list.supply_package = value
            elif field == "to":
                packing_list.to = value
            elif hasattr(packing_list, field):
                setattr(packing_list, field, value)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Packing list updated successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# VERIFICATION STATUS CRUD OPERATIONS

@router.get("/{list_id}/verification")
async def get_verification_items(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get verification items for a packing list.
    Equivalent to GET /:id/verification in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        
        return {
            "success": True,
            "data": {"verificationItems": verification_items}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get verification items error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/{list_id}/verification")
async def add_verification_item(
    list_id: str,
    verification_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add verification item.
    Equivalent to POST /:id/verification in Node.js packingList.ts
    """
    try:
        item = verification_data.get("item")
        status_val = verification_data.get("status")
        verified_by = verification_data.get("verifiedBy", "")
        
        if not item or not status_val:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Item and status are required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        new_verification_item = {
            "id": str(uuid4()),
            "item": item,
            "status": status_val,
            "verifiedBy": verified_by
        }
        
        verification_items.append(new_verification_item)
        packing_list.set_verification_items_data(verification_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item added successfully",
            "data": {"verificationItem": new_verification_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/verification/{verification_id}")
async def update_verification_item(
    list_id: str,
    verification_id: str,
    verification_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update verification item.
    Equivalent to PUT /:id/verification/:verificationId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        item_index = next((i for i, item in enumerate(verification_items) if item.get("id") == verification_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Verification item not found"
            )
        
        verification_items[item_index].update(verification_data)
        packing_list.set_verification_items_data(verification_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item updated successfully",
            "data": {"verificationItem": verification_items[item_index]}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}/verification/{verification_id}")
async def delete_verification_item(
    list_id: str,
    verification_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete verification item (Undo action).
    Equivalent to DELETE /:id/verification/:verificationId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        filtered_items = [item for item in verification_items if item.get("id") != verification_id]
        
        if len(filtered_items) == len(verification_items):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Verification item not found"
            )
        
        packing_list.set_verification_items_data(filtered_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item removed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# COMPLETION STATUS CRUD OPERATIONS

@router.get("/{list_id}/completion")
async def get_completion_status(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get completion status.
    Equivalent to GET /:id/completion in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        completion_status = packing_list.get_completion_status_data()
        if not completion_status:
            completion_status = {
                "allItemsPacked": False,
                "allItemsVerified": False,
                "deliveryInformationAdded": False,
                "finalApproval": False,
                "itemsPackedCount": 0,
                "totalItemsCount": 0,
                "itemsVerifiedCount": 0
            }
        
        return {
            "success": True,
            "data": {"completionStatus": completion_status}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get completion status error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/completion")
async def update_completion_status(
    list_id: str,
    completion_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update completion status.
    Equivalent to PUT /:id/completion in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        current_completion_status = packing_list.get_completion_status_data()
        updated_completion_status = {**current_completion_status, **completion_data}
        
        packing_list.set_completion_status_data(updated_completion_status)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Completion status updated successfully",
            "data": {"completionStatus": updated_completion_status}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update completion status error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# ITEMS TO UNPACK CRUD OPERATIONS

@router.get("/{list_id}/unpack")
async def get_unpack_items(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get unpack items for a packing list.
    Equivalent to GET /:id/unpack in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        
        return {
            "success": True,
            "data": {"unpackItems": unpack_items}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get unpack items error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/{list_id}/unpack")
async def add_unpack_item(
    list_id: str,
    unpack_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add unpack item.
    Equivalent to POST /:id/unpack in Node.js packingList.ts
    """
    try:
        item = unpack_data.get("item")
        status_val = unpack_data.get("status")
        total_qty = unpack_data.get("totalQty")
        unpacked_qty = unpack_data.get("unpackedQty", 0)
        
        if not item or not status_val or total_qty is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Item, status, and totalQty are required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        new_unpack_item = {
            "id": str(uuid4()),
            "item": item,
            "status": status_val,
            "totalQty": total_qty,
            "unpackedQty": unpacked_qty
        }
        
        unpack_items.append(new_unpack_item)
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item added successfully",
            "data": {"unpackItem": new_unpack_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/unpack/{unpack_id}")
async def update_unpack_item(
    list_id: str,
    unpack_id: str,
    unpack_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update unpack item.
    Equivalent to PUT /:id/unpack/:unpackId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        item_index = next((i for i, item in enumerate(unpack_items) if item.get("id") == unpack_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        # Update the item with new data
        unpack_items[item_index].update(unpack_data)
        
        # Auto-update status based on quantities
        updated_item = unpack_items[item_index]
        unpacked_qty = updated_item.get("unpackedQty", 0)
        total_qty = updated_item.get("totalQty", 0)
        
        if unpacked_qty == 0:
            updated_item["status"] = "Packed"
        elif unpacked_qty >= total_qty:
            updated_item["status"] = "Unpacked"
        else:
            updated_item["status"] = "Partially Unpacked"
        
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item updated successfully",
            "data": {"unpackItem": updated_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}/unpack/{unpack_id}")
async def delete_unpack_item(
    list_id: str,
    unpack_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete unpack item.
    Equivalent to DELETE /:id/unpack/:unpackId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        filtered_items = [item for item in unpack_items if item.get("id") != unpack_id]
        
        if len(filtered_items) == len(unpack_items):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        packing_list.set_unpack_items_data(filtered_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item removed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.patch("/{list_id}/unpack/{unpack_id}/quantity")
async def update_unpack_quantity(
    list_id: str,
    unpack_id: str,
    quantity_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Bulk update unpack quantities (for + and - buttons).
    Equivalent to PATCH /:id/unpack/:unpackId/quantity in Node.js packingList.ts
    """
    try:
        action = quantity_data.get("action")  # 'increment' or 'decrement'
        
        if not action or action not in ['increment', 'decrement']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Valid action (increment/decrement) is required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        item_index = next((i for i, item in enumerate(unpack_items) if item.get("id") == unpack_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        item = unpack_items[item_index]
        unpacked_qty = item.get("unpackedQty", 0)
        total_qty = item.get("totalQty", 0)
        
        if action == 'increment' and unpacked_qty < total_qty:
            item["unpackedQty"] = unpacked_qty + 1
        elif action == 'decrement' and unpacked_qty > 0:
            item["unpackedQty"] = unpacked_qty - 1
        
        # Auto-update status based on quantities
        new_unpacked_qty = item["unpackedQty"]
        if new_unpacked_qty == 0:
            item["status"] = "Packed"
        elif new_unpacked_qty >= total_qty:
            item["status"] = "Unpacked"
        else:
            item["status"] = "Partially Unpacked"
        
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Quantity updated successfully",
            "data": {"unpackItem": item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update quantity error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}")
async def delete_packing_list(
    list_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete packing list.
    Equivalent to DELETE /:id in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        db.delete(packing_list)
        db.commit()
        
        return {
            "success": True,
            "message": "Packing list deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )