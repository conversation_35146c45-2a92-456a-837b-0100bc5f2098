#!/usr/bin/env python3
"""
Database migration script for RBAC enhancements.
Adds proper indexing and updates existing data for hierarchical access control.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config.database import DATABASE_URL, get_db
from app.models.user import User
from app.models.assets import Asset
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the RBAC enhancement migration."""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        logger.info("Starting RBAC enhancement migration...")
        
        # 1. Add indexes for performance (if they don't exist)
        logger.info("Adding database indexes for RBAC fields...")
        
        index_queries = [
            "CREATE INDEX IF NOT EXISTS idx_users_access_level ON users(access_level)",
            "CREATE INDEX IF NOT EXISTS idx_users_state ON users(state)",
            "CREATE INDEX IF NOT EXISTS idx_users_county ON users(county)",
            "CREATE INDEX IF NOT EXISTS idx_users_precinct ON users(precinct)",
            "CREATE INDEX IF NOT EXISTS idx_assets_state ON assets(state)",
            "CREATE INDEX IF NOT EXISTS idx_assets_county ON assets(county)",
            "CREATE INDEX IF NOT EXISTS idx_assets_precinct ON assets(precinct)",
            "CREATE INDEX IF NOT EXISTS idx_asset_transfers_from_county ON asset_transfers(from_county)",
            "CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_county ON asset_transfers(to_county)",
            "CREATE INDEX IF NOT EXISTS idx_asset_transfers_from_precinct ON asset_transfers(from_precinct)",
            "CREATE INDEX IF NOT EXISTS idx_asset_transfers_to_precinct ON asset_transfers(to_precinct)"
        ]
        
        for query in index_queries:
            try:
                db.execute(text(query))
                logger.info(f"✓ Executed: {query}")
            except Exception as e:
                logger.warning(f"Index creation failed (may already exist): {e}")
        
        # 2. Update existing assets that don't have state field
        logger.info("Updating existing assets with default state values...")
        
        assets_without_state = db.query(Asset).filter(Asset.state.is_(None)).all()
        logger.info(f"Found {len(assets_without_state)} assets without state information")
        
        for asset in assets_without_state:
            # Set default state based on county or use a default
            if asset.county:
                # You can customize this mapping based on your actual state-county relationships
                asset.state = "South Carolina"  # Default state - customize as needed
            else:
                asset.state = "South Carolina"
                asset.county = "Default County"  # Set default county if missing
            
            logger.info(f"Updated asset {asset.asset_id} with state: {asset.state}, county: {asset.county}")
        
        # 3. Update existing users that don't have proper geographical scope
        logger.info("Updating existing users with proper geographical scope...")
        
        users_to_update = db.query(User).all()
        for user in users_to_update:
            updated = False
            
            # Ensure state-level users have state
            if user.access_level == "state" and not user.state:
                user.state = "South Carolina"  # Default state - customize as needed
                updated = True
                
            # Ensure county-level users have state and county
            elif user.access_level == "county":
                if not user.state:
                    user.state = "South Carolina"
                    updated = True
                if not user.county:
                    user.county = "Default County"  # Set default - should be updated by admin
                    updated = True
                    
            # Ensure precinct-level users have all three
            elif user.access_level == "precinct":
                if not user.state:
                    user.state = "South Carolina"
                    updated = True
                if not user.county:
                    user.county = "Default County"
                    updated = True
                if not user.precinct:
                    user.precinct = "Default Precinct"  # Set default - should be updated by admin
                    updated = True
            
            if updated:
                logger.info(f"Updated user {user.email} geographical scope")
        
        # 4. Commit all changes
        db.commit()
        logger.info("✓ Migration completed successfully!")
        
        # 5. Validation
        logger.info("Running validation checks...")
        
        # Check users with invalid geographical scope
        invalid_users = []
        for user in db.query(User).all():
            if not user.validate_geographical_scope():
                invalid_users.append(user.email)
        
        if invalid_users:
            logger.warning(f"⚠️  Found {len(invalid_users)} users with invalid geographical scope:")
            for email in invalid_users:
                logger.warning(f"  - {email}")
            logger.warning("These users should be updated by an administrator.")
        else:
            logger.info("✓ All users have valid geographical scope")
        
        # Check assets without required fields
        assets_without_required_fields = db.query(Asset).filter(
            (Asset.state.is_(None)) | (Asset.county.is_(None))
        ).count()
        
        if assets_without_required_fields > 0:
            logger.warning(f"⚠️  Found {assets_without_required_fields} assets without required geographical fields")
        else:
            logger.info("✓ All assets have required geographical fields")
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    run_migration()
